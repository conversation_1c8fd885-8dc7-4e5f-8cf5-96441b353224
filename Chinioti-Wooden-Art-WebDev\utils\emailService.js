const nodemailer = require("nodemailer");
require("dotenv").config();

// Create a transporter object
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

/**
 * Send a password reset email with OTP
 * @param {string} to - Recipient email
 * @param {string} otp - One-time password
 * @returns {Promise} - Nodemailer send mail promise
 */
const sendResetPasswordEmail = async (to, otp) => {
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to,
    subject: "Password Reset - Chinioti Wooden Art",
    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">Password Reset</h2>
                <p>You requested a password reset for your Chinioti Wooden Art account.</p>
                <p>Your OTP code is: <strong style="font-size: 18px; letter-spacing: 2px;">${otp}</strong></p>
                <p>This code will expire in 15 minutes.</p>
                <p>If you didn't request this, please ignore this email or contact support if you have concerns.</p>
                <div style="text-align: center; margin-top: 20px; color: #777; font-size: 12px;">
                    <p>Chinioti Wooden Art</p>
                </div>
            </div>
        `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
};

/**
 * Send a contact form email
 * @param {Object} contactData - Contact form data
 * @param {string} contactData.name - Sender's name
 * @param {string} contactData.email - Sender's email
 * @param {string} contactData.phone - Sender's phone (optional)
 * @param {string} contactData.subject - Email subject
 * @param {string} contactData.message - Email message
 * @returns {Promise} - Nodemailer send mail promise
 */
const sendContactFormEmail = async (contactData) => {
  const { name, email, phone, subject, message } = contactData;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: process.env.EMAIL_USER, // Send to the site admin
    replyTo: email, // Set reply-to as the sender's email
    subject: `Contact Form: ${subject}`,
    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">User needs your attention</h2>
                <div style="margin-top: 20px;">
                    <p><strong>Name:</strong> ${name}</p>
                    <p><strong>Email:</strong> ${email}</p>
                    ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ""}
                    <p><strong>Subject:</strong> ${subject}</p>
                    <div style="margin-top: 15px;">
                        <p><strong>Message:</strong></p>
                        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
                            ${message.replace(/\n/g, "<br>")}
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px; color: #777; font-size: 12px;">
                    <p>Chinioti Wooden Art - Contact Form Submission</p>
                    <p>&copy; ${new Date().getFullYear()} Chinioti Wooden Art</p>
                </div>
            </div>
        `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (error) {
    console.error("Error sending contact form email:", error);
    throw error;
  }
};

/**
 * Send an auto-reply email to the contact form submitter
 * @param {string} to - Recipient email
 * @param {string} name - Recipient name
 * @returns {Promise} - Nodemailer send mail promise
 */
const sendContactAutoReplyEmail = async (to, name) => {
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to,
    subject: "Thank you for contacting Chinioti Wooden Art",
    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">Thank You for Contacting Us</h2>
                <p>Dear ${name},</p>
                <p>Thank you for reaching out to Chinioti Wooden Art. We have received your message and will get back to you as soon as possible.</p>
                <p>If your inquiry is urgent, please call us at +92 123 4567890 during our business hours:</p>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li>Monday - Friday: 9:00 AM - 6:00 PM</li>
                    <li>Saturday: 10:00 AM - 4:00 PM</li>
                    <li>Sunday: Closed</li>
                </ul>
                <p>Best regards,</p>
                <p>The Chinioti Wooden Art Team</p>
                <div style="text-align: center; margin-top: 20px; color: #777; font-size: 12px;">
                    <p>&copy; ${new Date().getFullYear()} Chinioti Wooden Art. All rights reserved.</p>
                </div>
            </div>
        `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (error) {
    console.error("Error sending auto-reply email:", error);
    // Don't throw error for auto-reply to prevent blocking the main flow
    return null;
  }
};

/**
 * Send a notification email about a new product to multiple users
 * @param {Array} recipients - Array of email addresses
 * @param {Object} product - Product object with details
 * @returns {Promise} - Nodemailer send mail promise
 */
const sendNewProductNotification = async (recipients, product) => {
  // Format price with 2 decimal places
  const formattedPrice = parseFloat(product.price).toFixed(2);

  // Create a comma-separated list of emails for BCC
  const bccList = recipients.join(",");

  const mailOptions = {
    from: process.env.EMAIL_USER,
    bcc: bccList, // Use BCC to hide recipient emails from each other
    subject: `New Product Added: ${product.name} - Chinioti Wooden Art`,
    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">New Product Added!</h2>

                <div style="text-align: center; margin: 20px 0;">
                    <img src="${process.env.DEPLOYED_URL}/uploads/${
      product.image
    }" alt="${
      product.name
    }" style="max-width: 300px; max-height: 300px; border-radius: 5px;">
                </div>

                <h3 style="color: #333; margin-bottom: 5px;">${
                  product.name
                }</h3>
                <p style="color: #e63946; font-weight: bold; font-size: 18px; margin-top: 5px;">Price: $${formattedPrice}</p>

                <div style="margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <p style="margin: 0; color: #333;">${
                      product.description
                    }</p>
                </div>

                <div style="margin-top: 15px;">
                    <p><strong>Category:</strong> ${
                      product.category || "N/A"
                    }</p>
                    <p><strong>Type:</strong> ${product.type}</p>
                    <p><strong>In Stock:</strong> ${
                      product.stock ? "Yes" : "No"
                    }</p>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <a href="${process.env.CLIENT_URL}/product/${
      product._id
    }" style="background-color: #e63946; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Product</a>
                </div>

                <div style="text-align: center; margin-top: 30px; color: #777; font-size: 12px;">
                    <p>&copy; ${new Date().getFullYear()} Chinioti Wooden Art. All rights reserved.</p>
                    <p>If you no longer wish to receive these notifications, please update your preferences in your account settings.</p>
                </div>
            </div>
        `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Product notification emails sent successfully");
    return info;
  } catch (error) {
    console.error("Error sending product notification emails:", error);
    throw error;
  }
};

// module.exports = {
//     sendResetPasswordEmail,
//     sendNewProductNotification
module.exports = {
  sendResetPasswordEmail,
  sendContactFormEmail,
  sendContactAutoReplyEmail,
  sendNewProductNotification,
};
