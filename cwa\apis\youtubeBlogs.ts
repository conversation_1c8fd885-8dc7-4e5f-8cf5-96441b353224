// Types for YouTube blog data
export interface YouTubeBlogData {
  id: string;
  title: string;
  description: string;
  youtubeUrl: string;
  youtubeVideoId: string;
  thumbnailUrl: string;
  category: string;
  tags: string[];
  isActive: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  author?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface YouTubeBlogPagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface YouTubeBlogResponse {
  status: string;
  data: {
    blogs: YouTubeBlogData[];
    pagination: YouTubeBlogPagination;
  };
}

// Sample data for YouTube blogs (replace with actual API calls later)
const sampleYouTubeBlogs: YouTubeBlogData[] = [
  {
    id: "1",
    title: "Traditional Chinioti Wood Carving Techniques",
    description: "Watch master craftsmen demonstrate the ancient art of Chinioti wood carving, passed down through generations.",
    youtubeUrl: "https://www.youtube.com/shorts/yDiserKDRVo",
    youtubeVideoId: "yDiserKDRVo",
    thumbnailUrl: "https://i.ytimg.com/vi/yDiserKDRVo/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDf5TSXc0EUGvkZaDsqYiCJ32v-1w",
    category: "Craftsmanship",
    tags: ["woodworking", "traditional", "carving", "chinioti"],
    isActive: true,
    views: 1250,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
    author: {
      id: "1",
      name: "Hassan Ali",
      email: "<EMAIL>"
    }
  },
  {
    id: "2",
    title: "Behind the Scenes: Creating a Chinioti Dining Set",
    description: "Follow the complete process of creating a beautiful Chinioti dining set from raw wood to finished masterpiece.",
    youtubeUrl: "https://www.youtube.com/shorts/CyDlZBn7T00",
    youtubeVideoId: "CyDlZBn7T00",
    thumbnailUrl: "https://i.ytimg.com/vi/CyDlZBn7T00/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDf37RSYPpkZaiVvUiXijvXZoiT8A",
    category: "Craftsmanship",
    tags: ["dining set", "furniture", "process", "handmade"],
    isActive: true,
    views: 890,
    createdAt: "2024-01-10T14:30:00Z",
    updatedAt: "2024-01-10T14:30:00Z",
    author: {
      id: "2",
      name: "Hassan Ali",
      email: "<EMAIL>"
    }
  },
  {
    id: "3",
    title: "Wood Selection and Preparation for Fine Furniture",
    description: "Learn about the different types of wood used in Chinioti furniture and how they are prepared for crafting.",
    youtubeUrl: "https://www.youtube.com/shorts/MWfQXczPcAw",
    youtubeVideoId: "MWfQXczPcAw",
    thumbnailUrl: "https://i.ytimg.com/vi/MWfQXczPcAw/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLBGY7yiPrToXMM21uCInKcHWJ1kJg",
    category: "Craftsmanship",
    tags: ["wood selection", "materials", "preparation", "quality"],
    isActive: true,
    views: 567,
    createdAt: "2024-01-05T09:15:00Z",
    updatedAt: "2024-01-05T09:15:00Z",
    author: {
      id: "1",
      name: "Hassan Ali",
      email: "<EMAIL>"
    }
  },
  {
    id: "4",
    title: "Finishing Techniques for Wooden Furniture",
    description: "Discover the various finishing techniques that give Chinioti furniture its distinctive look and protection.",
    youtubeUrl: "https://www.youtube.com/shorts/46rOWwdXG2c",
    youtubeVideoId: "46rOWwdXG2c",
    thumbnailUrl: "https://i.ytimg.com/vi/46rOWwdXG2c/hq720_2.jpg?sqp=-oaymwEoCIIDEOADSFryq4qpAxoIARUAAIhC0AEB2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDMx_AzEv7lMnHlJoHqpCg7sjcL2w",
    category: "Craftsmanship",
    tags: ["finishing", "polish", "protection", "techniques"],
    isActive: true,
    views: 423,
    createdAt: "2024-01-03T16:20:00Z",
    updatedAt: "2024-01-03T16:20:00Z",
    author: {
      id: "2",
      name: "Hassan Ali",
      email: "<EMAIL>"
    }
  },
  {
    id: "5",
    title: "Maintenance Tips for Wooden Furniture",
    description: "Essential maintenance tips to keep your wooden furniture looking beautiful for years to come.",
    youtubeUrl: "https://www.youtube.com/shorts/yzfPOALe86A",
    youtubeVideoId: "yzfPOALe86A",
    thumbnailUrl: "https://i.ytimg.com/vi/yzfPOALe86A/hq720_2.jpg?sqp=-oaymwEoCJUDENAFSFryq4qpAxoIARUAAIhC0AEB2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLA4D-1Hvv9qIJwGpywKXRsdbo7BOg",
    category: "Craftsmanship",
    tags: ["maintenance", "care", "cleaning", "preservation"],
    isActive: true,
    views: 789,
    createdAt: "2024-01-01T11:30:00Z",
    updatedAt: "2024-01-01T11:30:00Z",
    author: {
      id: "1",
      name: "Hassan Ali",
      email: "<EMAIL>"
    }
  }
];

/**
 * Extract YouTube video ID from URL
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
};

/**
 * Get YouTube thumbnail URL
 */
export const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string => {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
};

/**
 * Get YouTube embed URL
 */
export const getYouTubeEmbedUrl = (videoId: string): string => {
  return `https://www.youtube.com/embed/${videoId}`;
};

/**
 * Get all YouTube blogs with pagination and optional search
 */
export const getYouTubeBlogs = async (
  page: number = 1,
  limit: number = 12,
  search?: string,
  category?: string
): Promise<{ blogs: YouTubeBlogData[]; pagination: YouTubeBlogPagination }> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredBlogs = [...sampleYouTubeBlogs];
    
    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredBlogs = filteredBlogs.filter(blog => 
        blog.title.toLowerCase().includes(searchLower) ||
        blog.description.toLowerCase().includes(searchLower) ||
        blog.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Apply category filter
    if (category && category !== 'all') {
      filteredBlogs = filteredBlogs.filter(blog => 
        blog.category.toLowerCase() === category.toLowerCase()
      );
    }
    
    // Calculate pagination
    const totalItems = filteredBlogs.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedBlogs = filteredBlogs.slice(startIndex, endIndex);
    
    return {
      blogs: paginatedBlogs,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    console.error("Error fetching YouTube blogs:", error);
    return {
      blogs: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: limit,
        hasNextPage: false,
        hasPrevPage: false
      }
    };
  }
};

/**
 * Get a single YouTube blog by ID
 */
export const getYouTubeBlogById = async (id: string): Promise<YouTubeBlogData | undefined> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return sampleYouTubeBlogs.find(blog => blog.id === id);
  } catch (error) {
    console.error(`Error fetching YouTube blog with ID ${id}:`, error);
    return undefined;
  }
};

/**
 * Get unique categories from YouTube blogs
 */
export const getYouTubeBlogCategories = async (): Promise<string[]> => {
  try {
    const categories = [...new Set(sampleYouTubeBlogs.map(blog => blog.category))];
    return categories;
  } catch (error) {
    console.error("Error fetching YouTube blog categories:", error);
    return [];
  }
};

/**
 * Add a new YouTube blog (for admin use)
 */
export const addYouTubeBlog = async (blogData: Omit<YouTubeBlogData, 'id' | 'createdAt' | 'updatedAt' | 'views'>): Promise<YouTubeBlogData | null> => {
  try {
    // Extract video ID from URL
    const videoId = extractYouTubeVideoId(blogData.youtubeUrl);
    if (!videoId) {
      throw new Error('Invalid YouTube URL');
    }

    const newBlog: YouTubeBlogData = {
      ...blogData,
      id: Date.now().toString(),
      youtubeVideoId: videoId,
      thumbnailUrl: getYouTubeThumbnail(videoId),
      views: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // In a real app, this would make an API call
    sampleYouTubeBlogs.unshift(newBlog);

    return newBlog;
  } catch (error) {
    console.error("Error adding YouTube blog:", error);
    return null;
  }
};

/**
 * Helper function to quickly add a YouTube video blog
 * Usage: addQuickYouTubeBlog("https://www.youtube.com/watch?v=VIDEO_ID", "Video Title", "Video Description", "Category")
 */
export const addQuickYouTubeBlog = async (
  youtubeUrl: string,
  title: string,
  description: string,
  category: string = "General",
  tags: string[] = [],
  author?: { id: string; name: string; email: string }
): Promise<YouTubeBlogData | null> => {
  const blogData = {
    title,
    description,
    youtubeUrl,
    youtubeVideoId: "", // Will be extracted automatically
    thumbnailUrl: "", // Will be generated automatically
    category,
    tags,
    isActive: true,
    author: author || {
      id: "1",
      name: "Admin",
      email: "<EMAIL>"
    }
  };

  return await addYouTubeBlog(blogData);
};

// Example usage (you can call this function to add videos):
// addQuickYouTubeBlog(
//   "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
//   "How to Care for Wooden Furniture",
//   "Learn the best practices for maintaining your wooden furniture to ensure it lasts for generations.",
//   "Care Tips",
//   ["furniture", "care", "maintenance", "wood"]
// );
