"use client";

import Footer from "@/sections/Footer";
import Header from "@/sections/Header";
import React, { ReactNode, useEffect } from "react";
import { CartProvider } from "@/contexts/CartContext";
import { OrderProvider } from "@/contexts/OrderContext";
import { WishlistProvider } from "@/contexts/WishlistContext";
import { ModalProvider } from "@/contexts/ModalContext";
import { TermsAcceptanceProvider } from "@/contexts/TermsAcceptanceContext";
import WhatsAppButton from "@/components/WhatsAppButton";
import BackToTopButton from "@/components/BackToTopButton";
import TermsAcceptanceModal from "@/components/TermsAcceptanceModal";
import { usePathname } from "next/navigation";
import {
  isGoogleAuthInProgress,
  clearGoogleAuthInProgress,
} from "@/lib/auth/authUtils";

const Layout = ({ children }: { children: ReactNode }) => {
  const pathname = usePathname();
  const isHomePage = pathname === "/";

  // Check for Google auth in progress on client side
  useEffect(() => {
    // If Google auth is in progress and we're on the home page,
    // this could indicate a redirect loop
    if (isGoogleAuthInProgress() && window.location.pathname === "/") {
      console.warn(
        "Detected potential Google auth redirect loop, clearing flag"
      );
      // Clear the flag to break the loop using our utility function
      clearGoogleAuthInProgress();
    }
  }, []);

  return (
    <CartProvider>
      <OrderProvider>
        <WishlistProvider>
          <ModalProvider>
            <TermsAcceptanceProvider>
              <div>
                {/* Only render Header if not on home page (Hero has integrated header) */}
                {!isHomePage && <Header />}
                <div className="relative">{children}</div>
                <Footer />
                <WhatsAppButton phoneNumber="03421401866" />
                <BackToTopButton />
                <TermsAcceptanceModal />
              </div>
            </TermsAcceptanceProvider>
          </ModalProvider>
        </WishlistProvider>
      </OrderProvider>
    </CartProvider>
  );
};

export default Layout;
