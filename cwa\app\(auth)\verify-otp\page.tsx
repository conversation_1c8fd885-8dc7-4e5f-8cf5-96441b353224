"use client";
import React, { useState, useRef, useEffect, Suspense } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { ArrowLeft } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

// Loading fallback component
const VerifyOTPLoading = () => {
  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <h2 className="text-2xl font-bold text-center mb-2">Verify OTP</h2>
        <p className="text-center text-muted-foreground mb-6 text-sm">
          Loading verification page...
        </p>
        <div className="flex justify-center py-8">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-accent animate-spin"
          >
            <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
          </svg>
        </div>
      </motion.div>
    </div>
  );
};

const VerifyOTP = () => {
  return (
    <Suspense fallback={<VerifyOTPLoading />}>
      <VerifyOTPContent />
    </Suspense>
  );
};

const VerifyOTPContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email") || "";
  const { forgotPassword, verifyOTP: verifyOTPApi, isLoading } = useAuth();

  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [timeLeft, setTimeLeft] = useState(120);

  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Set up countdown timer
  useEffect(() => {
    if (timeLeft <= 0) return;

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleOtpChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) return;

    // Update OTP array
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text");

    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split("");
      setOtp(digits);

      // Focus the last input
      inputRefs.current[5]?.focus();
    }
  };

  const handleResendOTP = async () => {
    if (timeLeft > 0) return;

    try {
      // Call the forgotPassword method to resend the OTP
      await forgotPassword(email);

      // Reset timer
      setTimeLeft(120);
    } catch (error) {
      // Error handling is done in the AuthContext
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if OTP is complete
    if (otp.some((digit) => !digit)) {
      toast.error("Please enter the complete OTP");
      return;
    }

    try {
      // Call the verifyOTP method from AuthContext
      const enteredOtp = otp.join("");
      const resetToken = await verifyOTPApi(email, enteredOtp);

      if (resetToken) {
        // Redirect to reset password page with the token
        router.push(`/reset-password/${resetToken}`);
      }
    } catch (error) {
      // Error handling is done in the AuthContext
    }
  };

  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Link
          href="/forgot-password"
          className="inline-flex items-center text-sm text-accent hover:underline mb-6"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
        </Link>

        <h2 className="text-2xl font-bold text-center mb-2">Verify OTP</h2>
        <p className="text-center text-muted-foreground mb-6 text-sm">
          We&apos;ve sent a 6-digit code to {email || "your email"}. Enter the
          code below to continue.
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex justify-center gap-2">
            {otp.map((digit, index) => (
              <motion.div
                key={index}
                whileFocus={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <input
                  ref={(el) => (inputRefs.current[index] = el)}
                  type="text"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={index === 0 ? handlePaste : undefined}
                  className="w-12 h-12 text-center text-lg font-bold border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                  required
                />
              </motion.div>
            ))}
          </div>

          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-2">
              {timeLeft > 0 ? (
                <>
                  Resend code in{" "}
                  <span className="font-medium">{formatTime(timeLeft)}</span>
                </>
              ) : (
                "Didn&apos;t receive the code?"
              )}
            </p>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleResendOTP}
              disabled={timeLeft > 0 || isLoading}
              className="text-accent hover:text-accent/80"
            >
              Resend OTP
            </Button>
          </div>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="submit"
              variant="default"
              className="w-full py-2"
              disabled={isLoading || otp.some((digit) => !digit)}
            >
              {isLoading ? "Verifying..." : "Verify OTP"}
            </Button>
          </motion.div>
        </form>
      </motion.div>
    </div>
  );
};

export default VerifyOTP;
