/**
 * Test script for Upstash Redis connection using the official Upstash Redis client
 * Run with: node test-redis.js
 */

require("dotenv").config();
const { Redis } = require("@upstash/redis");

// Create a new Redis instance using environment variables
console.log("Creating Upstash Redis client...");

// Extract Redis URL parts for logging (without exposing credentials)
const redisUrl = process.env.REDIS_URL || "";
const urlParts = redisUrl.split("@");
if (urlParts.length > 1) {
  console.log(`Connecting to Redis at: ${urlParts[1]}`);
} else {
  console.log("Redis URL not properly configured");
}

// Parse the Redis URL to extract the necessary parts
function parseRedisUrl(url) {
  try {
    // Format: redis://default:password@hostname:port
    const match = url.match(/redis:\/\/(.+):(.+)@(.+):(\d+)/);
    if (!match) {
      throw new Error("Invalid Redis URL format");
    }

    const [, username, password, host] = match;
    return {
      url: `https://${host}`,
      token: password,
    };
  } catch (error) {
    console.error("Error parsing Redis URL:", error.message);
    return null;
  }
}

// Test Upstash Redis
async function testUpstashRedis() {
  try {
    // Parse the Redis URL
    const redisConfig = parseRedisUrl(process.env.REDIS_URL);
    if (!redisConfig) {
      console.error("Failed to parse Redis URL");
      return;
    }

    // Create Redis client with the parsed configuration
    const redis = new Redis({
      url: redisConfig.url,
      token: redisConfig.token,
    });

    console.log("\nTesting basic Redis operations:");

    // Test SET operation
    const setResult = await redis.set(
      "test:upstash",
      "Hello from Chinioti Wooden Art!"
    );
    console.log("✅ SET operation successful:", setResult);

    // Test GET operation
    const getValue = await redis.get("test:upstash");
    console.log(`✅ GET operation successful: ${getValue}`);

    // Test expiration
    const setExResult = await redis.setex(
      "test:expiring",
      5,
      "This will expire in 5 seconds"
    );
    console.log("✅ SETEX operation successful:", setExResult);

    // Test EXISTS operation
    const existsResult = await redis.exists("test:upstash");
    console.log(
      `✅ EXISTS operation successful: ${
        existsResult === 1 ? "Key exists" : "Key does not exist"
      }`
    );

    // Wait for expiration
    console.log("Waiting 6 seconds for key to expire...");
    await new Promise((resolve) => setTimeout(resolve, 6000));

    // Check if expired key still exists
    const expiredExists = await redis.exists("test:expiring");
    console.log(
      `✅ Expiration test: ${
        expiredExists === 0 ? "Key expired successfully" : "Key did not expire"
      }`
    );

    // Clean up
    const delResult = await redis.del("test:upstash");
    console.log("✅ DEL operation successful:", delResult);

    console.log("\n🎉 All Redis operations completed successfully!");
    console.log("Your Upstash Redis connection is working properly.");
  } catch (error) {
    console.error("Error testing Upstash Redis:", error);
  }
}

// Run the test
testUpstashRedis();
