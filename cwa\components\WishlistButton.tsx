'use client';
import React from 'react';
import { Heart } from 'lucide-react';
import { useWishlist } from '@/contexts/WishlistContext';
import { motion } from 'framer-motion';

interface WishlistButtonProps {
  productId: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const WishlistButton: React.FC<WishlistButtonProps> = ({
  productId,
  size = 'md',
  showText = false,
  className = '',
}) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const isActive = isInWishlist(productId);

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isActive) {
      removeFromWishlist(productId);
    } else {
      addToWishlist(productId);
    }
  };

  // Size mappings
  const sizeMap = {
    sm: {
      button: 'p-1.5 rounded-full',
      icon: 16,
      text: 'text-xs',
    },
    md: {
      button: 'p-2 rounded-full',
      icon: 18,
      text: 'text-sm',
    },
    lg: {
      button: 'p-3 rounded-full',
      icon: 22,
      text: 'text-base',
    },
  };

  return (
    <motion.button
      whileTap={{ scale: 0.9 }}
      className={`${sizeMap[size].button} ${
        isActive
          ? 'bg-accent text-white'
          : 'bg-white text-gray-700 hover:bg-accent/10 hover:text-accent'
      } transition-colors flex items-center gap-1.5 ${className}`}
      onClick={handleToggleWishlist}
      aria-label={isActive ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      <Heart
        size={sizeMap[size].icon}
        className={isActive ? 'fill-white' : ''}
      />
      {showText && (
        <span className={sizeMap[size].text}>
          {isActive ? 'Saved' : 'Save'}
        </span>
      )}
    </motion.button>
  );
};

export default WishlistButton;
