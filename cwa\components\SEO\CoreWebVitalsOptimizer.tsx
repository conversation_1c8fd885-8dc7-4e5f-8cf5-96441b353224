"use client";

import { useEffect } from "react";
import Script from "next/script";

/**
 * Core Web Vitals Optimizer Component
 * Implements various optimizations for better Core Web Vitals scores
 */
export default function CoreWebVitalsOptimizer() {
  useEffect(() => {
    // Optimize CSS loading
    const optimizeCSSLoading = () => {
      // Load non-critical CSS asynchronously
      const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      stylesheets.forEach((link) => {
        const href = link.getAttribute('href');
        if (href && !href.includes('critical') && !href.includes('fonts')) {
          link.setAttribute('media', 'print');
          link.setAttribute('onload', "this.media='all'");
        }
      });
    };

    // Preload critical resources
    const preloadCriticalResources = () => {
      // Preload critical images if not already preloaded
      if (!document.querySelector('link[href="/assets/backgrounds/home-bg.png"]')) {
        const heroImageLink = document.createElement("link");
        heroImageLink.rel = "preload";
        heroImageLink.href = "/assets/backgrounds/home-bg.png";
        heroImageLink.as = "image";
        document.head.appendChild(heroImageLink);
      }

      // Preload logo if not already preloaded
      if (!document.querySelector('link[href="/favicon.svg"]')) {
        const logoLink = document.createElement("link");
        logoLink.rel = "preload";
        logoLink.href = "/favicon.svg";
        logoLink.as = "image";
        logoLink.type = "image/svg+xml";
        document.head.appendChild(logoLink);
      }
    };

    // Optimize images with lazy loading
    const optimizeImages = () => {
      const images = document.querySelectorAll("img[data-src]");
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || "";
            img.classList.remove("lazy");
            observer.unobserve(img);
          }
        });
      });

      images.forEach((img) => imageObserver.observe(img));
    };

    // Reduce layout shift by setting image dimensions
    const preventLayoutShift = () => {
      const images = document.querySelectorAll("img:not([width]):not([height])");
      images.forEach((img) => {
        const htmlImg = img as HTMLImageElement;
        if (htmlImg.naturalWidth && htmlImg.naturalHeight) {
          htmlImg.width = htmlImg.naturalWidth;
          htmlImg.height = htmlImg.naturalHeight;
        }
      });
    };

    // Optimize third-party scripts
    const optimizeThirdPartyScripts = () => {
      // Delay non-critical scripts
      const delayedScripts = document.querySelectorAll('script[data-delay]');
      const loadDelayedScripts = () => {
        delayedScripts.forEach((script) => {
          const newScript = document.createElement('script');
          newScript.src = script.getAttribute('data-src') || '';
          newScript.async = true;
          document.head.appendChild(newScript);
        });
      };

      // Load delayed scripts after user interaction or 3 seconds
      let userInteracted = false;
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      
      const triggerDelayedScripts = () => {
        if (!userInteracted) {
          userInteracted = true;
          setTimeout(loadDelayedScripts, 1000);
          events.forEach(event => {
            document.removeEventListener(event, triggerDelayedScripts, { passive: true });
          });
        }
      };

      events.forEach(event => {
        document.addEventListener(event, triggerDelayedScripts, { passive: true });
      });

      // Fallback: load after 3 seconds
      setTimeout(triggerDelayedScripts, 3000);
    };

    // Initialize optimizations
    optimizeCSSLoading();
    preloadCriticalResources();
    optimizeImages();
    preventLayoutShift();
    optimizeThirdPartyScripts();

    // Cleanup function
    return () => {
      // Remove event listeners if component unmounts
    };
  }, []);

  return (
    <>
      {/* Critical CSS inlining for above-the-fold content */}
      <style jsx>{`
        /* Critical CSS for Core Web Vitals */
        .hero-section {
          contain: layout style paint;
        }
        
        .lazy {
          opacity: 0;
          transition: opacity 0.3s;
        }
        
        .lazy.loaded {
          opacity: 1;
        }
        
        /* Prevent layout shift for images */
        img {
          max-width: 100%;
          height: auto;
        }
        
        /* Optimize font loading */
        @font-face {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 400;
          font-display: swap;
          src: local('Poppins Regular'), local('Poppins-Regular');
        }
      `}</style>

      {/* Web Vitals measurement script */}
      <Script
        id="web-vitals"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            function sendToAnalytics(metric) {
              // Send Core Web Vitals to analytics
              if (typeof gtag !== 'undefined') {
                gtag('event', metric.name, {
                  event_category: 'Web Vitals',
                  event_label: metric.id,
                  value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
                  non_interaction: true,
                });
              }
              
              // Log to console in development
              if (process.env.NODE_ENV === 'development') {
                console.log('Web Vital:', metric);
              }
            }

            // Measure Core Web Vitals
            if ('web-vital' in window) {
              import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
                getCLS(sendToAnalytics);
                getFID(sendToAnalytics);
                getFCP(sendToAnalytics);
                getLCP(sendToAnalytics);
                getTTFB(sendToAnalytics);
              });
            }
          `,
        }}
      />

      {/* Resource hints for better performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Preload critical resources */}
      <link
        rel="preload"
        href="/assets/backgrounds/home-bg.png"
        as="image"
        type="image/png"
      />
      <link
        rel="preload"
        href="/logo.svg"
        as="image"
        type="image/svg+xml"
      />
    </>
  );
}

// Helper function to optimize images for Core Web Vitals
export function optimizeImageForCWV(src: string, alt: string, priority = false) {
  return {
    src,
    alt,
    loading: priority ? "eager" : "lazy",
    decoding: "async",
    style: {
      contentVisibility: priority ? "visible" : "auto",
    },
  };
}

// Helper function to create optimized link preloads
export function createPreloadLink(href: string, as: string, type?: string, crossOrigin?: string) {
  const link = document.createElement("link");
  link.rel = "preload";
  link.href = href;
  link.as = as;
  if (type) link.type = type;
  if (crossOrigin) link.crossOrigin = crossOrigin;
  return link;
}

// Performance monitoring utilities
export const performanceUtils = {
  // Measure First Contentful Paint
  measureFCP: () => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find((entry) => entry.name === "first-contentful-paint");
        if (fcpEntry) {
          resolve(fcpEntry.startTime);
        }
      }).observe({ entryTypes: ["paint"] });
    });
  },

  // Measure Largest Contentful Paint
  measureLCP: () => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
      }).observe({ entryTypes: ["largest-contentful-paint"] });
    });
  },

  // Measure Cumulative Layout Shift
  measureCLS: () => {
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
    }).observe({ entryTypes: ["layout-shift"] });
    return clsValue;
  },

  // Get navigation timing
  getNavigationTiming: () => {
    const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
    return {
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      request: navigation.responseStart - navigation.requestStart,
      response: navigation.responseEnd - navigation.responseStart,
      dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
      load: navigation.loadEventEnd - navigation.loadEventStart,
    };
  },
};
