'use client';
import React, { useState } from 'react';
import { useCart } from '@/contexts/CartContext';
import { useOrders } from '@/contexts/OrderContext';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Minus, Plus, Trash2, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCurrency } from '@/contexts/CurrencyContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import WhatsAppOrderButton from '@/components/WhatsAppOrderButton';
import { WhatsAppOrderData } from '@/utils/whatsappOrder';
import { toast } from 'sonner';

const CartPage = () => {
  const { cart, updateQuantity, removeFromCart, clearCart } = useCart();
  const { createOrder } = useOrders();
  const { isAuthenticated, user } = useAuth();
  const { formatPrice, convertPrice, selectedCurrency } = useCurrency();
  const router = useRouter();
  const [showQuickOrderForm, setShowQuickOrderForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [quickOrderData, setQuickOrderData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
  });

  // Calculate discounted price - same logic as product detail page
  const getDiscountedPrice = (price: number, discount: string) => {
    return discount ? Math.round(price - (price * parseInt(discount)) / 100) : price;
  };

  const handleQuickOrderInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setQuickOrderData(prev => ({ ...prev, [name]: value }));
  };

  const handleQuickWhatsAppOrder = async (): Promise<WhatsAppOrderData> => {
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'phone', 'address', 'city'];
    const missingFields = requiredFields.filter(field => !quickOrderData[field as keyof typeof quickOrderData]);

    if (missingFields.length > 0) {
      toast.error('Please fill in all required fields');
      throw new Error('Missing required fields');
    }

    if (!isAuthenticated || !user?.id) {
      toast.error('You must be logged in to place an order');
      throw new Error('User not authenticated');
    }

    setIsLoading(true);

    try {
      // Create order data
      const orderData = {
        items: cart.items,
        total: cart.total,
        subtotal: cart.subtotal,
        discount: cart.discount,
        customer: {
          firstName: quickOrderData.firstName,
          lastName: quickOrderData.lastName,
          email: quickOrderData.email,
          phone: quickOrderData.phone,
        },
        shipping: {
          address: quickOrderData.address,
          city: quickOrderData.city,
          postalCode: quickOrderData.postalCode,
        },
        paymentMethod: "cash" as "cash" | "bank", // Default to cash for WhatsApp orders
        status: "pending" as const,
      };

      // Create order in database
      const createdOrder = await createOrder(orderData);

      // Prepare WhatsApp order data with the created order
      const whatsAppOrderData = {
        order: createdOrder,
        customerDetails: {
          firstName: quickOrderData.firstName,
          lastName: quickOrderData.lastName,
          email: quickOrderData.email,
          phone: quickOrderData.phone,
          address: quickOrderData.address,
          city: quickOrderData.city,
          postalCode: quickOrderData.postalCode,
        },
        formatPrice, // Pass the currency formatter from context
        selectedCurrency, // Pass the selected currency
      };

      // Clear cart after successful order creation
      clearCart();
      
      toast.success("Order created successfully! Redirecting to WhatsApp...");
      
      // Close the cart page by navigating to orders page after a short delay
      setTimeout(() => {
        router.push("/orders");
      }, 2000);
      
      // Return the WhatsApp order data for the button to handle
      return whatsAppOrderData;
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("Failed to create order. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-8">Shopping Cart</h1>

        {cart.items.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center py-16 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <ShoppingBag size={64} className="text-gray-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
            <p className="text-gray-500 mb-6">
              Looks like you haven't added any products to your cart yet.
            </p>
            <Link href="/products">
              <Button variant="default">Continue Shopping</Button>
            </Link>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-100">
                  <div className="flex justify-between items-center">
                    <h2 className="text-lg font-semibold">
                      Cart Items ({cart.totalItems})
                    </h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearCart}
                      className="text-red-500 hover:text-red-600 hover:bg-red-50"
                    >
                      Clear Cart
                    </Button>
                  </div>
                </div>

                <AnimatePresence>
                  {cart.items.map((item) => (
                    <motion.div
                      key={item.product.id}
                      className="p-4 border-b border-gray-100 flex flex-col sm:flex-row gap-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -100 }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Product Image */}
                      <div className="w-full sm:w-24 h-24 bg-gray-50 rounded-md overflow-hidden flex-shrink-0">
                        <Image
                          src={item.product.image}
                          alt={item.product.title}
                          width={96}
                          height={96}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <Link href={`/products/${item.product.id}`}>
                          <h3 className="font-medium text-gray-900 hover:text-accent transition-colors">
                            {item.product.title}
                          </h3>
                        </Link>
                        <p className="text-sm text-gray-500 mb-2">
                          {item.product.category}
                        </p>

                        <div className="flex flex-wrap justify-between items-center gap-2">
                          <div className="flex items-center">
                            {item.product.discount ? (
                              <>
                                <span className="font-semibold text-accent">
                                  {formatPrice(
                                    getDiscountedPrice(
                                      item.product.price,
                                      item.product.discount
                                    )
                                  )}
                                </span>
                                <span className="ml-2 text-sm text-gray-400 line-through">
                                  {formatPrice(item.product.price)}
                                </span>
                              </>
                            ) : (
                              <span className="font-semibold">
                                {formatPrice(item.product.price)}
                              </span>
                            )}
                          </div>

                          <div className="flex items-center gap-3">
                            {/* Quantity Controls */}
                            <div className="flex items-center border border-gray-200 rounded-md">
                              <button
                                onClick={() =>
                                  updateQuantity(
                                    item.product.id,
                                    item.quantity - 1
                                  )
                                }
                                className="px-2 py-1 text-gray-500 hover:text-accent transition-colors"
                                aria-label="Decrease quantity"
                              >
                                <Minus size={16} />
                              </button>
                              <span className="px-2 py-1 min-w-[2rem] text-center">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() =>
                                  updateQuantity(
                                    item.product.id,
                                    item.quantity + 1
                                  )
                                }
                                className="px-2 py-1 text-gray-500 hover:text-accent transition-colors"
                                aria-label="Increase quantity"
                              >
                                <Plus size={16} />
                              </button>
                            </div>

                            {/* Remove Button */}
                            <button
                              onClick={() => removeFromCart(item.product.id)}
                              className="text-gray-400 hover:text-red-500 transition-colors"
                              aria-label="Remove item"
                            >
                              <Trash2 size={18} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-4 sticky top-4">
                <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span>{formatPrice(cart.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Discount</span>
                    <span className="text-accent">
                      -{formatPrice(cart.discount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span>Free</span>
                  </div>
                  <div className="border-t border-gray-100 pt-2 mt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Total</span>
                      <span>{formatPrice(cart.total)}</span>
                    </div>
                  </div>
                </div>

                <Button
                  variant="default"
                  className="w-full mb-3"
                  onClick={() => setShowQuickOrderForm(!showQuickOrderForm)}
                  disabled={isLoading}
                >
                  {showQuickOrderForm ? 'Hide Order Form' : 'Order via WhatsApp'}
                </Button>

                <div className="text-center text-sm text-gray-500 mb-3">
                  Fill out your details to place order via WhatsApp
                </div>

                {showQuickOrderForm && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t border-gray-100 pt-4 mt-4"
                  >
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          name="firstName"
                          placeholder="First Name *"
                          value={quickOrderData.firstName}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                        <input
                          type="text"
                          name="lastName"
                          placeholder="Last Name *"
                          value={quickOrderData.lastName}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                      </div>
                      
                      <input
                        type="email"
                        name="email"
                        placeholder="Email Address"
                        value={quickOrderData.email}
                        onChange={handleQuickOrderInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                      />
                      
                      <input
                        type="tel"
                        name="phone"
                        placeholder="Phone Number *"
                        value={quickOrderData.phone}
                        onChange={handleQuickOrderInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        required
                      />
                      
                      <textarea
                        name="address"
                        placeholder="Delivery Address *"
                        value={quickOrderData.address}
                        onChange={handleQuickOrderInputChange}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        required
                      />
                      
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          name="city"
                          placeholder="City *"
                          value={quickOrderData.city}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                        <input
                          type="text"
                          name="postalCode"
                          placeholder="Postal Code"
                          value={quickOrderData.postalCode}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        />
                      </div>

                      <WhatsAppOrderButton
                        onOrderCreate={handleQuickWhatsAppOrder}
                        className="w-full"
                        disabled={isLoading || !quickOrderData.firstName || !quickOrderData.lastName || !quickOrderData.phone || !quickOrderData.address || !quickOrderData.city}
                      >
                        {isLoading ? 'Creating Order...' : 'Send Order via WhatsApp'}
                      </WhatsAppOrderButton>
                    </div>
                  </motion.div>
                )}

                <div className="mt-4">
                  <Link href="/products">
                    <Button variant="ghost" className="w-full">
                      Continue Shopping
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default CartPage;