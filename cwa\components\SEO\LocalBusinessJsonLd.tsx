"use client";

import <PERSON><PERSON><PERSON> from "next/script";
import { siteConfig } from "@/config/site";

interface LocalBusinessJsonLdProps {
  url: string;
}

/**
 * Generates JSON-LD structured data for a local business
 * This helps search engines understand the business information
 * and can enhance search results with rich snippets
 */
const LocalBusinessJsonLd = ({ url }: LocalBusinessJsonLdProps) => {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": ["FurnitureStore", "LocalBusiness", "Store"],
    "@id": `${url}/#localbusiness`,
    name: siteConfig.name,
    alternateName: [
      siteConfig.shortName,
      "Chiniot Furniture Store",
      "Authentic Chiniot Furniture",
      "Chinioti Wooden Art"
    ],
    description: `${siteConfig.description} Located in Chiniot, Punjab, Pakistan - the world's furniture manufacturing capital.`,
    image: [
      `${url}/og-image.jpg`,
      `${url}/logo.svg`,
      `${url}/assets/backgrounds/home-bg.png`
    ],
    url: url,
    telephone: siteConfig.contact.phone,
    email: siteConfig.contact.email,
    priceRange: siteConfig.seo.organization.priceRange,

    // Enhanced address with local context
    address: {
      "@type": "PostalAddress",
      streetAddress: siteConfig.contact.address.street,
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      postalCode: siteConfig.contact.address.postalCode,
      addressCountry: siteConfig.contact.address.country,
      addressCountryCode: "PK"
    },

    // Geographic coordinates
    geo: {
      "@type": "GeoCoordinates",
      latitude: siteConfig.seo.location.latitude,
      longitude: siteConfig.seo.location.longitude,
      address: {
        "@type": "PostalAddress",
        addressLocality: "Chiniot",
        addressRegion: "Punjab",
        addressCountry: "Pakistan"
      }
    },

    // Enhanced service area
    areaServed: [
      ...siteConfig.seo.organization.areaServed,
      {
        "@type": "Country",
        name: "Pakistan",
        sameAs: "https://en.wikipedia.org/wiki/Pakistan"
      },
      {
        "@type": "State",
        name: "Punjab",
        containedInPlace: {
          "@type": "Country",
          name: "Pakistan"
        }
      },
      {
        "@type": "City",
        name: "Chiniot",
        containedInPlace: {
          "@type": "State",
          name: "Punjab"
        },
        sameAs: "https://en.wikipedia.org/wiki/Chiniot"
      }
    ],

    // Business hours
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        opens: siteConfig.seo.businessHours.weekdays.open,
        closes: siteConfig.seo.businessHours.weekdays.close,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Saturday",
        opens: siteConfig.seo.businessHours.saturday.open,
        closes: siteConfig.seo.businessHours.saturday.close,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Sunday",
        opens: siteConfig.seo.businessHours.sunday.open,
        closes: siteConfig.seo.businessHours.sunday.close,
      },
    ],

    // Business details
    foundingDate: siteConfig.seo.organization.foundingDate,
    paymentAccepted: siteConfig.seo.organization.paymentAccepted,
    currenciesAccepted: siteConfig.seo.organization.currenciesAccepted,

    // Aggregate rating
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: siteConfig.seo.richSnippets.aggregateRating.ratingValue,
      reviewCount: siteConfig.seo.richSnippets.aggregateRating.reviewCount,
      bestRating: siteConfig.seo.richSnippets.aggregateRating.bestRating,
      worstRating: siteConfig.seo.richSnippets.aggregateRating.worstRating,
    },

    // Social media and external links
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
      siteConfig.social.youtube,
      "https://en.wikipedia.org/wiki/Chiniot"
    ],

    // Keywords for local SEO
    keywords: [
      "Chiniot furniture",
      "furniture Chiniot Punjab",
      "Pakistani furniture",
      "handcrafted wooden furniture Pakistan",
      "traditional furniture makers",
      "Chiniot artisans",
      "wooden furniture manufacturer Pakistan",
      "authentic Chiniot craftsmanship"
    ].join(", "),

    // Additional properties
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Specialization",
        value: "Traditional Chiniot Furniture Craftsmanship"
      },
      {
        "@type": "PropertyValue",
        name: "Heritage",
        value: "500+ Years of Woodworking Tradition"
      },
      {
        "@type": "PropertyValue",
        name: "Location Significance",
        value: "Located in Chiniot - World's Furniture Manufacturing Capital"
      }
    ]
  };

  return (
    <Script id="local-business-jsonld" type="application/ld+json">
      {JSON.stringify(jsonLd)}
    </Script>
  );
};

export default LocalBusinessJsonLd;
