"use client";

import { useEffect, useState } from "react";
import dynamic from "next/dynamic";

// Coordinates for Chiniot, Pakistan
const position: [number, number] = [31.7094062, 72.9703328];

// Dynamically import the map components with ssr disabled
const MapWithNoSSR = dynamic(() => import("./MapClientComponent"), {
  ssr: false,
  loading: () => (
    <div
      style={{ height: "400px", width: "100%" }}
      className="flex  items-center justify-center bg-gray-100 rounded-lg"
    >
      Loading Map...
    </div>
  ),
});

export default function MapComponent() {
  return <MapWithNoSSR />;
}
