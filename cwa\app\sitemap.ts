import { MetadataRoute } from 'next';
import { siteConfig } from '@/config/site';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Base URL for your site
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${siteConfig.seo.domain}`;

  // High priority static routes for SEO - optimized for Chiniot furniture
  const staticRoutes = [
    {
      url: `${baseUrl}`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about-us`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/video-blogs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/blogs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
    // SEO-focused category pages for Chiniot furniture
    {
      url: `${baseUrl}/categories/beds`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/tables`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/chairs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/wardrobes`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/dining-sets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/sofa-sets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/cabinets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/categories/dressing-tables`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    // Legal and policy pages
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms-of-service`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/shipping-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/return-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ];

  // Dynamic routes for products
  let productRoutes: MetadataRoute.Sitemap = [];
  let categoryRoutes: MetadataRoute.Sitemap = [];
  let blogRoutes: MetadataRoute.Sitemap = [];

  try {
    // Fetch products from your API
    const productsResponse = await fetch(`${process.env.BACKEND_BASE_URL}/api/products`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (productsResponse.ok) {
      const products = await productsResponse.json();

      // Create sitemap entries for each product with SEO optimization
      productRoutes = products.map((product: any) => ({
        url: `${baseUrl}/products/${product.id}`,
        lastModified: new Date(product.updatedAt || product.createdAt || new Date()),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      }));
    }
  } catch (error) {
    console.error('Error fetching products for sitemap:', error);
  }

  try {
    // Fetch categories from your API
    const categoriesResponse = await fetch(`${process.env.BACKEND_BASE_URL}/api/categories`, {
      next: { revalidate: 86400 } // Revalidate daily
    });

    if (categoriesResponse.ok) {
      const categories = await categoriesResponse.json();

      // Create sitemap entries for each category
      categoryRoutes = categories.map((category: any) => ({
        url: `${baseUrl}/categories/${category.slug || category.id}`,
        lastModified: new Date(category.updatedAt || category.createdAt || new Date()),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      }));
    }
  } catch (error) {
    console.error('Error fetching categories for sitemap:', error);
  }

  try {
    // Fetch blog posts/video blogs from your API
    const blogsResponse = await fetch(`${process.env.BACKEND_BASE_URL}/api/video-blogs`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (blogsResponse.ok) {
      const blogs = await blogsResponse.json();

      // Create sitemap entries for each blog post
      blogRoutes = blogs.map((blog: any) => ({
        url: `${baseUrl}/video-blogs/${blog.id}`,
        lastModified: new Date(blog.updatedAt || blog.createdAt || new Date()),
        changeFrequency: 'monthly' as const,
        priority: 0.5,
      }));
    }
  } catch (error) {
    console.error('Error fetching blogs for sitemap:', error);
  }

  // Combine all routes and sort by priority
  const allRoutes = [...staticRoutes, ...productRoutes, ...categoryRoutes, ...blogRoutes];

  // Sort by priority (highest first) for better SEO
  allRoutes.sort((a, b) => (b.priority || 0) - (a.priority || 0));

  return allRoutes;
}
