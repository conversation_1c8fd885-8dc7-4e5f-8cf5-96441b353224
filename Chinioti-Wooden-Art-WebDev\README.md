# Chinioti Wooden Art API Documentation

This document provides information about the RESTful APIs available in the Chinioti Wooden Art application.

## Base URL

All API endpoints are prefixed with: `http://localhost:5002/api`

## Authentication APIs

### Register User

- **URL**: `/auth/register`
- **Method**: `POST`
- **Description**: Register a new user in the system

**Request Body Schema:**
```json
{
  "name": "String (required, max 25 chars, only alphabets and spaces)",
  "email": "String (required, valid email format, unique)",
  "password": "String (required, min 8 chars, must contain uppercase, lowercase, number, and special char)",
  "phone": "String (required, exactly 11 digits)"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "_id": "ObjectId",
    "name": "String",
    "email": "String",
    "password": "String (hashed)",
    "phone": "String",
    "createdAt": "Date"
  }
}
```

### Login User

- **URL**: `/auth/login`
- **Method**: `POST`
- **Description**: Authenticate a user and get user details

**Request Body Schema:**
```json
{
  "email": "String (required)",
  "password": "String (required)"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "_id": "ObjectId",
    "name": "String",
    "email": "String",
    "password": "String (hashed)",
    "phone": "String",
    "createdAt": "Date"
  }
}
```

## User APIs

### Get All Users

- **URL**: `/users`
- **Method**: `GET`
- **Description**: Retrieve a list of all users

**Response:**
```json
[
  {
    "_id": "ObjectId",
    "name": "String",
    "email": "String",
    "password": "String (hashed)",
    "phone": "String",
    "createdAt": "Date"
  }
]
```

### Get User by ID

- **URL**: `/users/:id`
- **Method**: `GET`
- **Description**: Retrieve a specific user by their ID

**Response:**
```json
{
  "_id": "ObjectId",
  "name": "String",
  "email": "String",
  "password": "String (hashed)",
  "phone": "String",
  "createdAt": "Date"
}
```

### Update User

- **URL**: `/users/:id`
- **Method**: `PUT`
- **Description**: Update a user's information

**Request Body Schema:**
```json
{
  "name": "String (optional)",
  "email": "String (optional)",
  "password": "String (optional)",
  "phone": "String (optional)"
}
```

**Response:**
```json
{
  "_id": "ObjectId",
  "name": "String",
  "email": "String",
  "password": "String (hashed)",
  "phone": "String",
  "createdAt": "Date"
}
```

### Delete User

- **URL**: `/users/:id`
- **Method**: `DELETE`
- **Description**: Delete a user from the system

**Response:**
```json
{
  "message": "User deleted"
}
```

## Product APIs

### Create Product

- **URL**: `/products`
- **Method**: `POST`
- **Description**: Create a new product

**Request Body Schema:**
```json
{
  "name": "String (required)",
  "type": "String (required)",
  "price": "Number (required)",
  "image": "String (required)",
  "description": "String (required)",
  "quantity": "Number (required)",
  "stock": "Boolean (required)"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "product": {
      "_id": "ObjectId",
      "name": "String",
      "type": "String",
      "price": "Number",
      "image": "String",
      "description": "String",
      "quantity": "Number",
      "stock": "Boolean",
      "created": "Date"
    }
  }
}
```

### Get All Products

- **URL**: `/products`
- **Method**: `GET`
- **Description**: Retrieve a list of all products

**Response:**
```json
{
  "status": "success",
  "results": "Number",
  "data": {
    "products": [
      {
        "_id": "ObjectId",
        "name": "String",
        "type": "String",
        "price": "Number",
        "image": "String",
        "description": "String",
        "quantity": "Number",
        "stock": "Boolean",
        "created": "Date"
      }
    ]
  }
}
```

### Get Product by ID

- **URL**: `/products/:id`
- **Method**: `GET`
- **Description**: Retrieve a specific product by its ID

**Response:**
```json
{
  "status": "success",
  "data": {
    "product": {
      "_id": "ObjectId",
      "name": "String",
      "type": "String",
      "price": "Number",
      "image": "String",
      "description": "String",
      "quantity": "Number",
      "stock": "Boolean",
      "created": "Date"
    }
  }
}
```

### Update Product

- **URL**: `/products/:id`
- **Method**: `PATCH`
- **Description**: Update a product's information

**Request Body Schema:**
```json
{
  "name": "String (optional)",
  "type": "String (optional)",
  "price": "Number (optional)",
  "image": "String (optional)",
  "description": "String (optional)",
  "quantity": "Number (optional)",
  "stock": "Boolean (optional)"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "product": {
      "_id": "ObjectId",
      "name": "String",
      "type": "String",
      "price": "Number",
      "image": "String",
      "description": "String",
      "quantity": "Number",
      "stock": "Boolean",
      "created": "Date"
    }
  }
}
```

### Delete Product

- **URL**: `/products/:id`
- **Method**: `DELETE`
- **Description**: Delete a product from the system

**Response:**
```json
{
  "status": "success",
  "data": null
}
```

## Order APIs

### Create Order

- **URL**: `/orders`
- **Method**: `POST`
- **Description**: Create a new order

**Request Body Schema:**
```json
{
  "user": "ObjectId (reference to User)",
  "products": ["Array of ObjectIds (references to Products)"],
  "total": "Number",
  "status": "String (optional, default: 'pending')"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "order": {
      "_id": "ObjectId",
      "user": "ObjectId",
      "products": ["Array of ObjectIds"],
      "total": "Number",
      "status": "String"
    }
  }
}
```

### Get All Orders

- **URL**: `/orders`
- **Method**: `GET`
- **Description**: Retrieve a list of all orders

**Response:**
```json
{
  "status": "success",
  "results": "Number",
  "data": {
    "orders": [
      {
        "_id": "ObjectId",
        "user": "ObjectId",
        "products": ["Array of ObjectIds"],
        "total": "Number",
        "status": "String"
      }
    ]
  }
}
```

### Get Order by ID

- **URL**: `/orders/:id`
- **Method**: `GET`
- **Description**: Retrieve a specific order by its ID

**Response:**
```json
{
  "status": "success",
  "data": {
    "order": {
      "_id": "ObjectId",
      "user": "ObjectId",
      "products": ["Array of ObjectIds"],
      "total": "Number",
      "status": "String"
    }
  }
}
```

### Update Order

- **URL**: `/orders/:id`
- **Method**: `PATCH`
- **Description**: Update an order's information

**Request Body Schema:**
```json
{
  "user": "ObjectId (optional)",
  "products": ["Array of ObjectIds (optional)"],
  "total": "Number (optional)",
  "status": "String (optional)"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "order": {
      "_id": "ObjectId",
      "user": "ObjectId",
      "products": ["Array of ObjectIds"],
      "total": "Number",
      "status": "String"
    }
  }
}
```

### Delete Order

- **URL**: `/orders/:id`
- **Method**: `DELETE`
- **Description**: Delete an order from the system

**Response:**
```json
{
  "status": "success",
  "data": null
}
```

## Error Responses

All API endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "status": "fail",
  "message": "Error details"
}
```

### 401 Unauthorized
```json
{
  "error": "Invalid credentials"
}
```

### 404 Not Found
```json
{
  "status": "fail",
  "message": "Error details"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error"
}
```
