"use client";

import { ProductData } from "@/types";
import <PERSON>ript from "next/script";
import { siteConfig, getUrl } from "@/config/site";

interface ProductJsonLdProps {
  product: ProductData;
  url?: string;
  breadcrumbs?: Array<{
    name: string;
    url: string;
  }>;
}

/**
 * Enhanced Product JSON-LD structured data for Chiniot furniture
 * Includes comprehensive product information, reviews, and local business context
 */
const ProductJsonLd = ({ product, url, breadcrumbs }: ProductJsonLdProps) => {
  const baseUrl = url || getUrl();
  const productUrl = `${baseUrl}/products/${product.id}`;

  // Calculate the discounted price if available
  const discountedPrice =
    product.discount && product.discount !== "0"
      ? Math.round(
          product.price - product.price * (parseInt(product.discount) / 100)
        )
      : null;

  // Format product images
  const productImages = product.images && product.images.length > 0
    ? product.images.map((img: string) =>
        img.startsWith("http") ? img : getUrl(img)
      )
    : product.image
    ? [product.image.startsWith("http") ? product.image : getUrl(product.image)]
    : [getUrl("/og-image.jpg")];

  // Enhanced Product Schema with Chiniot furniture context
  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "@id": productUrl,
    name: `${product.title || product.name} - Authentic Chiniot Furniture`,
    description: product.description
      ? `${product.description} Handcrafted in Chiniot, Punjab, Pakistan by skilled artisans using traditional techniques and premium wood.`
      : `Premium quality ${product.category} furniture from ${siteConfig.name}. Handcrafted in Chiniot, Punjab, Pakistan using traditional woodworking techniques.`,
    image: productImages,
    url: productUrl,
    sku: product.id,
    productID: product.id,
    model: product.title || product.name,

    brand: {
      "@type": "Brand",
      name: siteConfig.name,
      logo: getUrl("/logo.svg"),
      url: baseUrl
    },

    manufacturer: {
      "@type": "Organization",
      name: siteConfig.name,
      url: baseUrl,
      logo: getUrl("/logo.svg"),
      address: {
        "@type": "PostalAddress",
        streetAddress: siteConfig.contact.address.street,
        addressLocality: siteConfig.contact.address.city,
        addressRegion: siteConfig.contact.address.region,
        postalCode: siteConfig.contact.address.postalCode,
        addressCountry: siteConfig.contact.address.country
      }
    },

    category: `${product.category} Furniture`,

    // Enhanced offers with local business context
    offers: {
      "@type": "Offer",
      "@id": `${productUrl}#offer`,
      url: productUrl,
      price: discountedPrice || product.price,
      priceCurrency: "PKR", // Changed to Pakistani Rupee
      availability: product.available
        ? "https://schema.org/InStock"
        : "https://schema.org/OutOfStock",
      itemCondition: "https://schema.org/NewCondition",
      priceValidUntil: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      ).toISOString().split("T")[0],

      seller: {
        "@type": "Organization",
        name: siteConfig.name,
        url: baseUrl,
        telephone: siteConfig.contact.phone,
        email: siteConfig.contact.email,
        address: {
          "@type": "PostalAddress",
          streetAddress: siteConfig.contact.address.street,
          addressLocality: siteConfig.contact.address.city,
          addressRegion: siteConfig.contact.address.region,
          postalCode: siteConfig.contact.address.postalCode,
          addressCountry: siteConfig.contact.address.country
        }
      },

      // Shipping information for Pakistan
      shippingDetails: {
        "@type": "OfferShippingDetails",
        shippingRate: {
          "@type": "MonetaryAmount",
          currency: "PKR",
          value: "0"
        },
        deliveryTime: {
          "@type": "ShippingDeliveryTime",
          handlingTime: {
            "@type": "QuantitativeValue",
            minValue: 1,
            maxValue: 3,
            unitCode: "DAY"
          },
          transitTime: {
            "@type": "QuantitativeValue",
            minValue: 3,
            maxValue: 7,
            unitCode: "DAY"
          }
        },
        shippingDestination: {
          "@type": "DefinedRegion",
          addressCountry: "PK"
        }
      },

      // Return policy
      hasMerchantReturnPolicy: {
        "@type": "MerchantReturnPolicy",
        applicableCountry: "PK",
        returnPolicyCategory: "https://schema.org/MerchantReturnFiniteReturnWindow",
        merchantReturnDays: 7,
        returnMethod: "https://schema.org/ReturnByMail",
        returnFees: "https://schema.org/FreeReturn"
      }
    },

    // Enhanced product properties for Chiniot furniture
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Country of Origin",
        value: "Pakistan"
      },
      {
        "@type": "PropertyValue",
        name: "Crafted In",
        value: "Chiniot, Punjab, Pakistan"
      },
      {
        "@type": "PropertyValue",
        name: "Craftsmanship",
        value: "Handcrafted by Traditional Artisans"
      },
      {
        "@type": "PropertyValue",
        name: "Material",
        value: "Premium Wood"
      },
      {
        "@type": "PropertyValue",
        name: "Style",
        value: "Traditional Chiniot Design"
      }
    ],

    // Keywords for better discoverability
    keywords: [
      product.title || product.name,
      product.category,
      "Chiniot furniture",
      "Pakistani furniture",
      "handcrafted furniture",
      "wooden furniture",
      "traditional craftsmanship"
    ].join(", "),

    // Audience targeting
    audience: {
      "@type": "Audience",
      audienceType: "Furniture Buyers",
      geographicArea: {
        "@type": "AdministrativeArea",
        name: "Pakistan"
      }
    },

    // Additional context for Chiniot furniture
    isRelatedTo: [
      {
        "@type": "Thing",
        name: "Chiniot Furniture Industry",
        description: "Traditional furniture making industry of Chiniot, Punjab, Pakistan"
      },
      {
        "@type": "Place",
        name: "Chiniot",
        description: "Historic city in Punjab, Pakistan famous for furniture craftsmanship"
      }
    ]
  };

  // Breadcrumb Schema if provided
  const breadcrumbSchema = breadcrumbs && breadcrumbs.length > 0 ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: crumb.url.startsWith('http') ? crumb.url : getUrl(crumb.url)
    }))
  } : null;

  return (
    <>
      {/* Main Product Schema */}
      <Script
        id={`product-schema-${product.id}`}
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(productSchema)
        }}
      />

      {/* Breadcrumb Schema */}
      {breadcrumbSchema && (
        <Script
          id={`breadcrumb-schema-${product.id}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbSchema)
          }}
        />
      )}
    </>
  );
};

export default ProductJsonLd;
