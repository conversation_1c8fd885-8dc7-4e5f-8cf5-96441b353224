"use client";

import { useEffect, useState } from "react";

interface SEOIssue {
  type: "error" | "warning" | "info";
  category: string;
  message: string;
  element?: HTMLElement;
  fix?: string;
}

/**
 * SEO Validator Component
 * Tests and validates SEO implementations across the website
 */
export default function SEOValidator() {
  const [issues, setIssues] = useState<SEOIssue[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      validateSEO();
    }
  }, []);

  const validateSEO = async () => {
    setIsValidating(true);
    const foundIssues: SEOIssue[] = [];

    // Validate meta tags
    foundIssues.push(...validateMetaTags());
    
    // Validate heading hierarchy
    foundIssues.push(...validateHeadingHierarchy());
    
    // Validate images
    foundIssues.push(...validateImages());
    
    // Validate links
    foundIssues.push(...validateLinks());
    
    // Validate structured data
    foundIssues.push(...await validateStructuredData());
    
    // Validate accessibility
    foundIssues.push(...validateAccessibility());
    
    // Validate Core Web Vitals elements
    foundIssues.push(...validateCoreWebVitals());

    setIssues(foundIssues);
    setIsValidating(false);

    // Log results in development
    if (foundIssues.length > 0) {
      console.group("🔍 SEO Validation Results");
      foundIssues.forEach(issue => {
        const emoji = issue.type === "error" ? "❌" : issue.type === "warning" ? "⚠️" : "ℹ️";
        console.log(`${emoji} [${issue.category}] ${issue.message}`);
        if (issue.fix) console.log(`   💡 Fix: ${issue.fix}`);
      });
      console.groupEnd();
    } else {
      console.log("✅ All SEO validations passed!");
    }
  };

  const validateMetaTags = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];

    // Check title tag
    const title = document.querySelector("title");
    if (!title) {
      issues.push({
        type: "error",
        category: "Meta Tags",
        message: "Missing title tag",
        fix: "Add a <title> tag to the document head"
      });
    } else if (title.textContent && title.textContent.length > 60) {
      issues.push({
        type: "warning",
        category: "Meta Tags",
        message: `Title too long (${title.textContent.length} chars, recommended: 50-60)`,
        fix: "Shorten the title to 50-60 characters"
      });
    } else if (title.textContent && title.textContent.length < 30) {
      issues.push({
        type: "warning",
        category: "Meta Tags",
        message: `Title too short (${title.textContent.length} chars, recommended: 50-60)`,
        fix: "Expand the title to 50-60 characters"
      });
    }

    // Check meta description
    const description = document.querySelector('meta[name="description"]');
    if (!description) {
      issues.push({
        type: "error",
        category: "Meta Tags",
        message: "Missing meta description",
        fix: "Add a meta description tag"
      });
    } else {
      const content = description.getAttribute("content");
      if (content && content.length > 160) {
        issues.push({
          type: "warning",
          category: "Meta Tags",
          message: `Meta description too long (${content.length} chars, recommended: 150-160)`,
          fix: "Shorten the meta description to 150-160 characters"
        });
      } else if (content && content.length < 120) {
        issues.push({
          type: "warning",
          category: "Meta Tags",
          message: `Meta description too short (${content.length} chars, recommended: 150-160)`,
          fix: "Expand the meta description to 150-160 characters"
        });
      }
    }

    // Check Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    const ogDescription = document.querySelector('meta[property="og:description"]');
    const ogImage = document.querySelector('meta[property="og:image"]');

    if (!ogTitle) {
      issues.push({
        type: "warning",
        category: "Open Graph",
        message: "Missing og:title",
        fix: "Add Open Graph title meta tag"
      });
    }

    if (!ogDescription) {
      issues.push({
        type: "warning",
        category: "Open Graph",
        message: "Missing og:description",
        fix: "Add Open Graph description meta tag"
      });
    }

    if (!ogImage) {
      issues.push({
        type: "warning",
        category: "Open Graph",
        message: "Missing og:image",
        fix: "Add Open Graph image meta tag"
      });
    }

    return issues;
  };

  const validateHeadingHierarchy = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];
    const headings = document.querySelectorAll("h1, h2, h3, h4, h5, h6");
    
    let h1Count = 0;
    let lastLevel = 0;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (level === 1) {
        h1Count++;
      }

      if (index === 0 && level !== 1) {
        issues.push({
          type: "warning",
          category: "Heading Hierarchy",
          message: "First heading should be H1",
          element: heading as HTMLElement,
          fix: "Change the first heading to H1"
        });
      }

      if (level > lastLevel + 1) {
        issues.push({
          type: "warning",
          category: "Heading Hierarchy",
          message: `Heading hierarchy skipped from H${lastLevel} to H${level}`,
          element: heading as HTMLElement,
          fix: "Use sequential heading levels"
        });
      }

      lastLevel = level;
    });

    if (h1Count === 0) {
      issues.push({
        type: "error",
        category: "Heading Hierarchy",
        message: "No H1 heading found",
        fix: "Add an H1 heading to the page"
      });
    } else if (h1Count > 1) {
      issues.push({
        type: "warning",
        category: "Heading Hierarchy",
        message: `Multiple H1 headings found (${h1Count})`,
        fix: "Use only one H1 heading per page"
      });
    }

    return issues;
  };

  const validateImages = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];
    const images = document.querySelectorAll("img");

    images.forEach((img) => {
      if (!img.alt) {
        issues.push({
          type: "error",
          category: "Images",
          message: "Image missing alt text",
          element: img,
          fix: "Add descriptive alt text to the image"
        });
      } else if (img.alt.length < 10) {
        issues.push({
          type: "warning",
          category: "Images",
          message: "Alt text too short (should be descriptive)",
          element: img,
          fix: "Make alt text more descriptive"
        });
      }

      if (!img.loading && !img.getAttribute("priority")) {
        issues.push({
          type: "info",
          category: "Performance",
          message: "Image could benefit from lazy loading",
          element: img,
          fix: "Add loading='lazy' attribute"
        });
      }
    });

    return issues;
  };

  const validateLinks = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];
    const links = document.querySelectorAll("a");

    links.forEach((link) => {
      if (!link.textContent?.trim() && !link.getAttribute("aria-label")) {
        issues.push({
          type: "error",
          category: "Links",
          message: "Link has no accessible text",
          element: link,
          fix: "Add text content or aria-label to the link"
        });
      }

      if (link.href && link.href.startsWith("http") && !link.href.includes(window.location.hostname)) {
        if (!link.rel?.includes("noopener")) {
          issues.push({
            type: "warning",
            category: "Security",
            message: "External link missing rel='noopener'",
            element: link,
            fix: "Add rel='noopener' to external links"
          });
        }
      }
    });

    return issues;
  };

  const validateStructuredData = async (): Promise<SEOIssue[]> => {
    const issues: SEOIssue[] = [];
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');

    if (scripts.length === 0) {
      issues.push({
        type: "warning",
        category: "Structured Data",
        message: "No structured data found",
        fix: "Add JSON-LD structured data"
      });
      return issues;
    }

    scripts.forEach((script) => {
      try {
        const data = JSON.parse(script.textContent || "");
        
        if (!data["@context"]) {
          issues.push({
            type: "error",
            category: "Structured Data",
            message: "Missing @context in structured data",
            fix: "Add @context property to structured data"
          });
        }

        if (!data["@type"]) {
          issues.push({
            type: "error",
            category: "Structured Data",
            message: "Missing @type in structured data",
            fix: "Add @type property to structured data"
          });
        }
      } catch (error) {
        issues.push({
          type: "error",
          category: "Structured Data",
          message: "Invalid JSON-LD syntax",
          fix: "Fix JSON syntax in structured data"
        });
      }
    });

    return issues;
  };

  const validateAccessibility = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];

    // Check for main landmark
    const main = document.querySelector("main");
    if (!main) {
      issues.push({
        type: "warning",
        category: "Accessibility",
        message: "Missing main landmark",
        fix: "Add a <main> element to the page"
      });
    }

    // Check for skip link
    const skipLink = document.querySelector('a[href="#main-content"], a[href="#main"]');
    if (!skipLink) {
      issues.push({
        type: "info",
        category: "Accessibility",
        message: "Missing skip to main content link",
        fix: "Add a skip link for keyboard navigation"
      });
    }

    // Check touch targets
    const interactiveElements = document.querySelectorAll("button, a, input, select, textarea");
    interactiveElements.forEach((element) => {
      const rect = element.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        issues.push({
          type: "warning",
          category: "Accessibility",
          message: "Touch target too small (minimum 44px)",
          element: element as HTMLElement,
          fix: "Increase touch target size to at least 44px"
        });
      }
    });

    return issues;
  };

  const validateCoreWebVitals = (): SEOIssue[] => {
    const issues: SEOIssue[] = [];

    // Check for large images without optimization
    const images = document.querySelectorAll("img");
    images.forEach((img) => {
      if (img.naturalWidth > 1920 && !img.srcset) {
        issues.push({
          type: "warning",
          category: "Core Web Vitals",
          message: "Large image without responsive sizes",
          element: img,
          fix: "Add srcset or use Next.js Image component"
        });
      }
    });

    // Check for render-blocking resources
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    stylesheets.forEach((link) => {
      if (!link.getAttribute("media") && !link.hasAttribute("async")) {
        issues.push({
          type: "info",
          category: "Core Web Vitals",
          message: "Potentially render-blocking stylesheet",
          element: link as HTMLElement,
          fix: "Consider inlining critical CSS or using media queries"
        });
      }
    });

    return issues;
  };

  // Only render in development
  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={validateSEO}
        disabled={isValidating}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 disabled:opacity-50"
      >
        {isValidating ? "Validating..." : "🔍 SEO Check"}
      </button>
      
      {issues.length > 0 && (
        <div className="mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-w-md max-h-96 overflow-y-auto">
          <div className="p-3 border-b border-gray-200">
            <h3 className="font-semibold text-sm">SEO Issues ({issues.length})</h3>
          </div>
          <div className="p-3 space-y-2">
            {issues.map((issue, index) => (
              <div key={index} className="text-xs">
                <div className={`font-medium ${
                  issue.type === "error" ? "text-red-600" : 
                  issue.type === "warning" ? "text-yellow-600" : 
                  "text-blue-600"
                }`}>
                  {issue.type === "error" ? "❌" : issue.type === "warning" ? "⚠️" : "ℹ️"} {issue.category}
                </div>
                <div className="text-gray-700">{issue.message}</div>
                {issue.fix && (
                  <div className="text-gray-500 italic">💡 {issue.fix}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Export validation functions for external use
export const seoValidationUtils = {
  validateMetaTags: () => {
    const validator = new SEOValidator();
    return validator;
  },
  
  checkPageSEO: () => {
    console.log("🔍 Running SEO validation...");
    // This would trigger the validation
  }
};
