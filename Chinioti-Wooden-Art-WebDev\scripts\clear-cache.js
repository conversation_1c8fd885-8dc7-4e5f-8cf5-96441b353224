/**
 * Utility script to clear specific Redis cache keys
 * Run with: node scripts/clear-cache.js [key pattern]
 * 
 * Examples:
 *   - Clear all keys: node scripts/clear-cache.js "*"
 *   - Clear all API keys: node scripts/clear-cache.js "api:*"
 *   - Clear specific key: node scripts/clear-cache.js "api:/api/products"
 */

require('dotenv').config();
const { redisClient, connectRedis, clearCache, clearCacheKey } = require('../config/redis');

async function main() {
  try {
    // Connect to Redis
    const connected = await connectRedis();
    if (!connected) {
      console.error('Failed to connect to Redis. Check your Redis URL.');
      process.exit(1);
    }

    // Get key pattern from command line arguments
    const pattern = process.argv[2] || 'api:/api/products';
    
    if (pattern.includes('*')) {
      // Clear keys by pattern
      console.log(`Clearing cache keys matching pattern: ${pattern}`);
      await clearCache(pattern);
    } else {
      // Clear specific key
      console.log(`Clearing specific cache key: ${pattern}`);
      const result = await clearCacheKey(pattern);
      if (result) {
        console.log(`Successfully cleared cache key: ${pattern}`);
      } else {
        console.log(`No action taken for key: ${pattern}`);
      }
    }

    console.log('Cache clearing operation completed.');
    process.exit(0);
  } catch (error) {
    console.error('Error clearing cache:', error);
    process.exit(1);
  }
}

main();
