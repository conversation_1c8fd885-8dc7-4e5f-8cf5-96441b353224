@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: var(--color-zinc-400);
  /* --primary: oklch(0.74 0.1591 67.83); */
  --primary-foreground: var(--color-zinc-600);
  --secondary: var(--color-zinc-700);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: var(--color-amber-600);
  /* --accent: oklch(0.97 0 0); */
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: var(--color-amber-600);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: var(--color-zinc-500);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: var(--color-amber-600);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins), system-ui, -apple-system,
      BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
  }
}

@layer utilities {
  /* Custom responsive utilities for extra small screens */
  @media (min-width: 375px) {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
    .xs\:block {
      display: block;
    }
  }

  /* Touch-friendly minimum sizes for mobile */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }


  /* Exception for text links within paragraphs */
  p a,
  li a,
  span a {
    min-height: auto;
    min-width: auto;
    display: inline;
  }

  /* Enhanced touch targets for mobile navigation */
  .mobile-nav-item {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Improved mobile header spacing */
  .mobile-header-spacing {
    padding-left: clamp(0.75rem, 4vw, 1.5rem);
    padding-right: clamp(0.75rem, 4vw, 1.5rem);
  }

  /* Mobile sidebar navigation styles */
  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-sidebar.open {
    transform: translateX(0);
  }

  /* Mobile sidebar overlay */
  .mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  }

  .mobile-sidebar-overlay.open {
    opacity: 1;
    visibility: visible;
  }

  /* Hamburger menu animation */
  .hamburger-line {
    display: block;
    width: 20px;
    height: 2px;
    background: currentColor;
    transition: all 0.3s ease-in-out;
    transform-origin: center;
  }

  .hamburger-line:not(:last-child) {
    margin-bottom: 4px;
  }

  .hamburger.open .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.open .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger.open .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Mobile-first responsive breakpoints */

  /* Base mobile styles (320px+) */
  .responsive-header {
    padding: 0;
  }

  .responsive-logo {
    font-size: 1.125rem; /* 18px */
    line-height: 1.25;
  }

  .responsive-nav-item {
    font-size: 0.875rem; /* 14px */
    padding: 0.75rem 1rem;
  }

  /* Small mobile (375px+) */
  @media (min-width: 375px) {
    .responsive-header {
      padding: 0;
    }

    .responsive-logo {
      font-size: 1.25rem; /* 20px */
    }

    .responsive-nav-item {
      font-size: 0.9375rem; /* 15px */
      padding: 0.875rem 1.25rem;
    }
  }

  /* Large mobile/small tablet (640px+) */
  @media (min-width: 640px) {
    .responsive-header {
      padding: 0;
    }

    .responsive-logo {
      font-size: 1.375rem; /* 22px */
    }

    .responsive-nav-item {
      font-size: 1rem; /* 16px */
      padding: 1rem 1.5rem;
    }
  }

  /* Tablet (768px+) */
  @media (min-width: 768px) {
    .responsive-header {
      padding: 0;
    }

    .responsive-logo {
      font-size: 1.5rem; /* 24px */
    }

    .mobile-sidebar,
    .mobile-sidebar-overlay {
      display: none;
    }
  }

  /* Desktop (1024px+) */
  @media (min-width: 1024px) {
    .responsive-header {
      padding: 0;
    }

    .responsive-logo {
      font-size: 1.625rem; /* 26px */
    }
  }

  /* Header content overflow prevention */
  .responsive-header .container {
    max-width: 100%;
    overflow: hidden;
  }

  /* Ensure header content fits properly */
  @media (max-width: 767px) {
    .responsive-header {
      max-width: 100vw;
      overflow-x: hidden;
    }
  }

  /* Focus styles for accessibility */
  .focus-visible {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
  }

  /* Smooth scrolling for sidebar navigation */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Prevent body scroll when sidebar is open */
  .no-scroll {
    overflow: hidden;
    height: 100vh;
  }

  /* Hero header toolbar styling */
  .hero-toolbar .text-foreground {
    color: white !important;
  }

  .hero-toolbar svg {
    color: white !important;
  }

  .hero-toolbar .text-gray-700 {
    color: white !important;
  }

  .hero-toolbar .border-black {
    border-color: white !important;
  }

  .hero-toolbar .hover\:bg-accent\/10:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Profile component responsive utilities */
  @media (max-width: 374px) {
    .profile-container {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .profile-card {
      margin-left: 0 !important;
      margin-right: 0 !important;
      border-radius: 0.5rem !important;
    }

    .profile-header {
      padding: 1rem !important;
    }

    .profile-content {
      padding: 1rem !important;
    }
  }

  /* Extra small screens (320px+) */
  @media (min-width: 320px) and (max-width: 374px) {
    .profile-avatar {
      width: 2.5rem !important;
      height: 2.5rem !important;
    }

    .profile-name {
      font-size: 1rem !important;
      line-height: 1.25rem !important;
    }

    .profile-email {
      font-size: 0.75rem !important;
      line-height: 1rem !important;
    }
  }

  /* Toast positioning - force toasts to appear at the top */
  [data-sonner-toaster] {
    top: 40px !important;
    bottom: auto !important;
  }

  /* Ensure toasts are above other elements */
  [data-sonner-toast] {
    z-index: 9999 !important;
  }
}
