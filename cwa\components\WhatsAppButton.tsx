'use client';
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaWhatsapp } from 'react-icons/fa';
import { useModal } from '@/contexts/ModalContext';

interface WhatsAppButtonProps {
  phoneNumber: string;
  message?: string;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({
  phoneNumber,
  message = 'Hello! I have a question about your products.',
}) => {
  const { isAnyModalOpen } = useModal();
  // Format the phone number to ensure it works with WhatsApp
  const formattedPhoneNumber = phoneNumber.startsWith('+')
    ? phoneNumber.substring(1)
    : phoneNumber.startsWith('0')
    ? `92${phoneNumber.substring(1)}`
    : phoneNumber;

  // Create the WhatsApp URL
  const whatsappUrl = `https://wa.me/${formattedPhoneNumber}?text=${encodeURIComponent(
    message
  )}`;

  return (
    <AnimatePresence>
      {!isAnyModalOpen && (
        <motion.div
          className="fixed bottom-6 right-6 z-40 flex flex-col items-end gap-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        >
      {/* Tooltip - Only visible on hover on desktop */}
      <motion.div
        className="hidden md:block bg-white text-gray-800 text-sm py-1 px-3 rounded-lg shadow-md mb-1"
        initial={{ opacity: 0, scale: 0.8, y: 10 }}
        whileHover={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        Chat with us on WhatsApp
      </motion.div>

      {/* WhatsApp Button */}
      <motion.a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="bg-green-500 text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center justify-center"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label="Chat on WhatsApp"
      >
        <FaWhatsapp size={28} />
      </motion.a>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WhatsAppButton;
