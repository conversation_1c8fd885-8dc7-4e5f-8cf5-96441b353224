"use client";
import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const InvalidResetToken = () => {
  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4"
        >
          <X className="h-8 w-8 text-destructive" />
        </motion.div>
        
        <h2 className="text-2xl font-bold mb-2">Invalid Reset Link</h2>
        
        <p className="text-muted-foreground mb-6">
          This password reset link is invalid or has expired. Please request a new password reset link.
        </p>
        
        <Link href="/forgot-password">
          <Button variant="default">Request New Reset Link</Button>
        </Link>
      </motion.div>
    </div>
  );
};

export default InvalidResetToken;
