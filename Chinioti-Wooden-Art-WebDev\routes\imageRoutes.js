const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { 
  findFileById, 
  createReadStream, 
  findFileByFilename 
} = require('../config/gridfs');

// Get image by filename
router.get('/file/:filename', async (req, res) => {
  try {
    const file = await findFileByFilename(req.params.filename);
    
    if (!file) {
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }
    
    // Set the appropriate content type
    res.set('Content-Type', file.metadata.mimetype);
    
    // Create a read stream and pipe it to the response
    const readStream = createReadStream(file._id);
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving image:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving image'
    });
  }
});

// Get image by ID
router.get('/id/:id', async (req, res) => {
  try {
    const id = req.params.id;
    
    // Validate if the ID is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid image ID'
      });
    }
    
    const file = await findFileById(id);
    
    if (!file) {
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }
    
    // Set the appropriate content type
    res.set('Content-Type', file.metadata.mimetype);
    
    // Create a read stream and pipe it to the response
    const readStream = createReadStream(file._id);
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving image:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving image'
    });
  }
});

module.exports = router;
