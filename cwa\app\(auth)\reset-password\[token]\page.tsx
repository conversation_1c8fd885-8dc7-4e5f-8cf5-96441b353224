'use client';
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Check, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const ResetPassword = () => {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;
  const { resetPassword, isLoading } = useAuth();

  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [isTokenValid, setIsTokenValid] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);

  // Password strength criteria
  const [passwordCriteria, setPasswordCriteria] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecial: false,
  });

  // Validate token on page load
  useEffect(() => {
    const validateToken = async () => {
      try {
        // Simulate API call to validate token
        await new Promise((resolve) => setTimeout(resolve, 500));

        // For demo purposes, we'll consider tokens with "invalid" in them as invalid
        if (token.includes('invalid')) {
          setIsTokenValid(false);
          toast.error('This password reset link is invalid or has expired.');
        }
      } catch (error) {
        setIsTokenValid(false);
        toast.error('Failed to validate reset link.');
      }
    };

    validateToken();
  }, [token]);

  // Update password criteria whenever password changes
  useEffect(() => {
    const { password } = formData;
    setPasswordCriteria({
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecial: /[^A-Za-z0-9]/.test(password),
    });
  }, [formData.password]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.password || !formData.confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    // Check if all password criteria are met
    const allCriteriaMet = Object.values(passwordCriteria).every(Boolean);
    if (!allCriteriaMet) {
      toast.error('Please ensure your password meets all requirements');
      return;
    }

    try {
      // Call the resetPassword method from AuthContext
      await resetPassword(token, formData.password);

      setIsSuccess(true);

      // Redirect to sign-in page after 3 seconds
      setTimeout(() => {
        router.push('/sign-in');
      }, 3000);
    } catch (error) {
      // Error handling is done in the AuthContext
    }
  };

  // If token is invalid, show error message
  if (!isTokenValid) {
    return (
      <div className="bg-card rounded-lg shadow-lg p-8 w-full">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center"
        >
          <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
            <X className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Invalid Reset Link</h2>
          <p className="text-muted-foreground mb-6">
            This password reset link is invalid or has expired.
          </p>
          <Link href="/forgot-password">
            <Button variant="default">Request New Reset Link</Button>
          </Link>
        </motion.div>
      </div>
    );
  }

  // If password reset is successful, show success message
  if (isSuccess) {
    return (
      <div className="bg-card rounded-lg shadow-lg p-8 w-full">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center"
        >
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Password Reset Successful</h2>
          <p className="text-muted-foreground mb-6">
            Your password has been reset successfully. You will be redirected to
            the sign-in page shortly.
          </p>
          <Link href="/sign-in">
            <Button variant="default">Sign In Now</Button>
          </Link>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Link
          href="/verify-otp"
          className="inline-flex items-center text-sm text-accent hover:underline mb-6"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
        </Link>

        <h2 className="text-2xl font-bold text-center mb-2">
          Reset Your Password
        </h2>
        <p className="text-center text-muted-foreground mb-6 text-sm">
          Please create a new password for your account
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium">
              New Password
            </label>
            <motion.div whileFocus={{ scale: 1.01 }} className="relative">
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                placeholder="••••••••"
              />
            </motion.div>

            {/* Password strength indicators */}
            <div className="mt-2 space-y-1">
              <p className="text-xs font-medium mb-1">Password must contain:</p>
              <div className="grid grid-cols-2 gap-1">
                <div
                  className={`text-xs flex items-center ${
                    passwordCriteria.minLength
                      ? 'text-green-600'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div
                    className={`mr-1 w-3 h-3 rounded-full ${
                      passwordCriteria.minLength ? 'bg-green-600' : 'bg-muted'
                    }`}
                  ></div>
                  At least 8 characters
                </div>
                <div
                  className={`text-xs flex items-center ${
                    passwordCriteria.hasUppercase
                      ? 'text-green-600'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div
                    className={`mr-1 w-3 h-3 rounded-full ${
                      passwordCriteria.hasUppercase
                        ? 'bg-green-600'
                        : 'bg-muted'
                    }`}
                  ></div>
                  Uppercase letter
                </div>
                <div
                  className={`text-xs flex items-center ${
                    passwordCriteria.hasLowercase
                      ? 'text-green-600'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div
                    className={`mr-1 w-3 h-3 rounded-full ${
                      passwordCriteria.hasLowercase
                        ? 'bg-green-600'
                        : 'bg-muted'
                    }`}
                  ></div>
                  Lowercase letter
                </div>
                <div
                  className={`text-xs flex items-center ${
                    passwordCriteria.hasNumber
                      ? 'text-green-600'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div
                    className={`mr-1 w-3 h-3 rounded-full ${
                      passwordCriteria.hasNumber ? 'bg-green-600' : 'bg-muted'
                    }`}
                  ></div>
                  Number
                </div>
                <div
                  className={`text-xs flex items-center ${
                    passwordCriteria.hasSpecial
                      ? 'text-green-600'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div
                    className={`mr-1 w-3 h-3 rounded-full ${
                      passwordCriteria.hasSpecial ? 'bg-green-600' : 'bg-muted'
                    }`}
                  ></div>
                  Special character
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium"
            >
              Confirm New Password
            </label>
            <motion.div whileFocus={{ scale: 1.01 }} className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                placeholder="••••••••"
              />
            </motion.div>
            {formData.password &&
              formData.confirmPassword &&
              formData.password !== formData.confirmPassword && (
                <p className="text-xs text-destructive mt-1">
                  Passwords do not match
                </p>
              )}
          </div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="pt-2"
          >
            <Button
              type="submit"
              variant="default"
              className="w-full py-2"
              disabled={isLoading}
            >
              {isLoading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </motion.div>
        </form>
      </motion.div>
    </div>
  );
};

export default ResetPassword;
