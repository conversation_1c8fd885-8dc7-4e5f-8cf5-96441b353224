"use client";
import React from "react";
import { motion } from "framer-motion";

interface HamburgerMenuProps {
  isOpen: boolean;
  onClick: () => void;
  className?: string;
  size?: "sm" | "md" | "lg";
  color?: string;
  ariaLabel?: string;
}

const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  isOpen,
  onClick,
  className = "",
  size = "md",
  color = "currentColor",
  ariaLabel = "Toggle navigation menu",
}) => {
  const sizeClasses = {
    sm: "w-5 h-5",
    md: "w-6 h-6",
    lg: "w-7 h-7",
  };

  const lineWidths = {
    sm: "16px",
    md: "20px",
    lg: "24px",
  };

  const lineHeight = "2px";
  const spacing = size === "sm" ? "3px" : size === "md" ? "4px" : "5px";

  return (
    <button
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          onClick();
        }
      }}
      className={`
        touch-target
        flex items-center justify-center
        p-2 rounded-md
        hover:bg-accent/10
        focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2
        transition-colors duration-200
        ${sizeClasses[size]}
        ${className}
      `}
      aria-label={ariaLabel}
      aria-expanded={isOpen}
      aria-controls="mobile-navigation"
      aria-haspopup="true"
      type="button"
      role="button"
      tabIndex={0}
    >
      <div className="relative flex flex-col justify-center items-center">
        {/* Top line */}
        <motion.span
          className="block bg-current"
          style={{
            width: lineWidths[size],
            height: lineHeight,
            backgroundColor: color,
          }}
          animate={{
            rotate: isOpen ? 45 : 0,
            y: isOpen ? `calc(${lineHeight} + ${spacing})` : 0,
          }}
          transition={{
            duration: 0.3,
            ease: "easeInOut",
          }}
        />
        
        {/* Middle line */}
        <motion.span
          className="block bg-current"
          style={{
            width: lineWidths[size],
            height: lineHeight,
            backgroundColor: color,
            marginTop: spacing,
            marginBottom: spacing,
          }}
          animate={{
            opacity: isOpen ? 0 : 1,
            scale: isOpen ? 0.8 : 1,
          }}
          transition={{
            duration: 0.3,
            ease: "easeInOut",
          }}
        />
        
        {/* Bottom line */}
        <motion.span
          className="block bg-current"
          style={{
            width: lineWidths[size],
            height: lineHeight,
            backgroundColor: color,
          }}
          animate={{
            rotate: isOpen ? -45 : 0,
            y: isOpen ? `calc(-${lineHeight} - ${spacing})` : 0,
          }}
          transition={{
            duration: 0.3,
            ease: "easeInOut",
          }}
        />
      </div>
    </button>
  );
};

export default HamburgerMenu;
