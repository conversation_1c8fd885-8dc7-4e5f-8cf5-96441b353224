import apiClient from '@/lib/api/apiClient';

// Types for video blog data
export interface VideoBlogData {
  id: string;
  title: string;
  videoFileId: string;
  videoFilename: string;
  mimetype: string;
  fileSize: number;
  duration?: number;
  thumbnailFileId?: string;
  thumbnailFilename?: string;
  isActive: boolean;
  uploadedBy: {
    id: string;
    name: string;
    email: string;
  };
  views: number;
  videoUrl: string;
  thumbnailUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface VideoBlogPagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface VideoBlogResponse {
  status: string;
  data: {
    videoBlogs: VideoBlogData[];
    pagination: VideoBlogPagination;
  };
}

export interface SingleVideoBlogResponse {
  status: string;
  data: {
    videoBlog: VideoBlogData;
  };
}

// Cache for video blogs
let videoBlogsCache: VideoBlogData[] | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Map backend video blog data to frontend format
 */
const mapBackendVideoBlogToFrontend = (backendVideoBlog: any): VideoBlogData => {
  return {
    id: backendVideoBlog._id || backendVideoBlog.id,
    title: backendVideoBlog.title,
    videoFileId: backendVideoBlog.videoFileId,
    videoFilename: backendVideoBlog.videoFilename,
    mimetype: backendVideoBlog.mimetype,
    fileSize: backendVideoBlog.fileSize,
    duration: backendVideoBlog.duration,
    thumbnailFileId: backendVideoBlog.thumbnailFileId,
    thumbnailFilename: backendVideoBlog.thumbnailFilename,
    isActive: backendVideoBlog.isActive,
    uploadedBy: backendVideoBlog.uploadedBy,
    views: backendVideoBlog.views,
    videoUrl: backendVideoBlog.videoUrl,
    thumbnailUrl: backendVideoBlog.thumbnailUrl,
    createdAt: backendVideoBlog.createdAt,
    updatedAt: backendVideoBlog.updatedAt,
  };
};

/**
 * Get all video blogs with pagination and optional search
 */
export const getVideoBlogs = async (
  page: number = 1,
  limit: number = 12,
  search?: string
): Promise<{ videoBlogs: VideoBlogData[]; pagination: VideoBlogPagination }> => {
  try {
    console.log("Fetching video blogs from API");
    
    // Build query parameters
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }

    const response = await apiClient.get(`/video-blogs?${params.toString()}`);

    if (response.data && response.data.status === "success") {
      const backendVideoBlogs = response.data.data.videoBlogs;
      const mappedVideoBlogs = backendVideoBlogs.map(mapBackendVideoBlogToFrontend);

      return {
        videoBlogs: mappedVideoBlogs,
        pagination: response.data.data.pagination
      };
    } else {
      console.error("API returned unexpected format:", response.data);
      return {
        videoBlogs: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }
  } catch (error) {
    console.error("Error fetching video blogs:", error);
    return {
      videoBlogs: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: limit,
        hasNextPage: false,
        hasPrevPage: false
      }
    };
  }
};

/**
 * Get a single video blog by ID
 */
export const getVideoBlogById = async (
  id: string
): Promise<VideoBlogData | undefined> => {
  try {
    const response = await apiClient.get(`/video-blogs/${id}`);

    if (response.data && response.data.status === "success") {
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return undefined;
    }
  } catch (error) {
    console.error(`Error fetching video blog with ID ${id}:`, error);
    return undefined;
  }
};

/**
 * Get video stream URL
 */
export const getVideoStreamUrl = (fileId: string): string => {
  return `/api/video-blogs/stream/${fileId}`;
};

/**
 * Get thumbnail URL
 */
export const getThumbnailUrl = (fileId: string): string => {
  return `/api/video-blogs/thumbnail/${fileId}`;
};

/**
 * Create a new video blog (Admin only)
 */
export const createVideoBlog = async (
  title: string,
  videoFile: File,
  thumbnailFile?: File
): Promise<VideoBlogData | null> => {
  try {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('video', videoFile);
    
    if (thumbnailFile) {
      formData.append('thumbnail', thumbnailFile);
    }

    const response = await apiClient.post('/video-blogs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data && response.data.status === "success") {
      // Clear cache
      videoBlogsCache = null;
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return null;
    }
  } catch (error) {
    console.error("Error creating video blog:", error);
    throw error;
  }
};

/**
 * Update a video blog (Admin only)
 */
export const updateVideoBlog = async (
  id: string,
  updates: { title?: string; isActive?: boolean }
): Promise<VideoBlogData | null> => {
  try {
    const response = await apiClient.patch(`/video-blogs/${id}`, updates);

    if (response.data && response.data.status === "success") {
      // Clear cache
      videoBlogsCache = null;
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return null;
    }
  } catch (error) {
    console.error(`Error updating video blog with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a video blog (Admin only)
 */
export const deleteVideoBlog = async (id: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`/video-blogs/${id}`);

    if (response.data && response.data.status === "success") {
      // Clear cache
      videoBlogsCache = null;
      return true;
    } else {
      console.error("API returned unexpected format:", response.data);
      return false;
    }
  } catch (error) {
    console.error(`Error deleting video blog with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format duration for display
 */
export const formatDuration = (seconds: number): string => {
  if (!seconds) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
};
