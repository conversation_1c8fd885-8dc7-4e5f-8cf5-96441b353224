/**
 * <PERSON><PERSON><PERSON> to test GridFS functionality
 * 
 * This script:
 * 1. Connects to MongoDB and initializes GridFS
 * 2. Uploads a test image to GridFS
 * 3. Retrieves the image from GridFS
 * 4. Deletes the image from GridFS
 */
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Initialize GridFS bucket
let gridFSBucket;

// Connect to GridFS
const connectGridFS = async () => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      throw new Error('MongoDB not connected');
    }

    // Initialize GridFS bucket
    gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS connected successfully');
    return true;
  } catch (error) {
    console.error('GridFS connection error:', error);
    return false;
  }
};

// Upload a file to GridFS
const uploadFileToGridFS = (filePath, filename, mimetype) => {
  return new Promise((resolve, reject) => {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return reject(new Error(`File not found: ${filePath}`));
      }

      // Create read stream from file
      const readStream = fs.createReadStream(filePath);
      
      // Create write stream to GridFS
      const writeStream = gridFSBucket.openUploadStream(filename, {
        metadata: {
          originalname: path.basename(filePath),
          mimetype: mimetype || 'image/jpeg', // Default to JPEG if mimetype not provided
          uploadDate: new Date()
        }
      });
      
      // Handle errors
      readStream.on('error', (error) => {
        reject(error);
      });
      
      writeStream.on('error', (error) => {
        reject(error);
      });
      
      // Handle completion
      writeStream.on('finish', (file) => {
        resolve(file);
      });
      
      // Pipe read stream to write stream
      readStream.pipe(writeStream);
    } catch (error) {
      reject(error);
    }
  });
};

// Find a file by filename
const findFileByFilename = async (filename) => {
  try {
    const files = await gridFSBucket.find({ filename }).toArray();
    return files.length > 0 ? files[0] : null;
  } catch (error) {
    console.error('Error finding file by filename:', error);
    return null;
  }
};

// Delete a file by ID
const deleteFileById = async (id) => {
  try {
    await gridFSBucket.delete(new mongoose.Types.ObjectId(id));
    return true;
  } catch (error) {
    console.error('Error deleting file by ID:', error);
    return false;
  }
};

// Download a file from GridFS
const downloadFileFromGridFS = (fileId, destinationPath) => {
  return new Promise((resolve, reject) => {
    try {
      // Create write stream to destination
      const writeStream = fs.createWriteStream(destinationPath);
      
      // Create read stream from GridFS
      const readStream = gridFSBucket.openDownloadStream(new mongoose.Types.ObjectId(fileId));
      
      // Handle errors
      readStream.on('error', (error) => {
        reject(error);
      });
      
      writeStream.on('error', (error) => {
        reject(error);
      });
      
      // Handle completion
      writeStream.on('finish', () => {
        resolve(true);
      });
      
      // Pipe read stream to write stream
      readStream.pipe(writeStream);
    } catch (error) {
      reject(error);
    }
  });
};

// Main test function
const testGridFS = async () => {
  try {
    // Connect to MongoDB and GridFS
    await connectDB();
    await connectGridFS();
    
    // Create test directory if it doesn't exist
    const testDir = path.join(__dirname, 'test-output');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    // Test image path (use an existing image from uploads directory)
    const uploadsDir = path.join(__dirname, '../uploads');
    const files = fs.readdirSync(uploadsDir);
    
    if (files.length === 0) {
      console.error('No files found in uploads directory');
      process.exit(1);
    }
    
    const testImageName = files[0];
    const testImagePath = path.join(uploadsDir, testImageName);
    
    console.log(`Using test image: ${testImageName}`);
    
    // Upload test image to GridFS
    console.log('Uploading test image to GridFS...');
    const uploadedFile = await uploadFileToGridFS(testImagePath, `test-${testImageName}`, 'image/jpeg');
    console.log('Upload successful:', uploadedFile);
    
    // Find the uploaded file
    console.log('Finding uploaded file...');
    const foundFile = await findFileByFilename(`test-${testImageName}`);
    console.log('Found file:', foundFile);
    
    // Download the file
    console.log('Downloading file from GridFS...');
    const downloadPath = path.join(testDir, `downloaded-${testImageName}`);
    await downloadFileFromGridFS(foundFile._id, downloadPath);
    console.log(`File downloaded to: ${downloadPath}`);
    
    // Delete the file
    console.log('Deleting file from GridFS...');
    await deleteFileById(foundFile._id);
    console.log('File deleted successfully');
    
    console.log('All tests passed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the test
testGridFS();
