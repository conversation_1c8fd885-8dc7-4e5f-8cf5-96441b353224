"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "leaflet-defaulticon-compatibility";
import "leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css";


// Coordinates for Chiniot, Pakistan
const position: [number, number] = [31.7094062, 72.9703328];

// Component to handle map initialization
function MapContent() {
  useEffect(() => {
    // Fix Leaflet icon issues
    // Use type assertion to avoid TypeScript error
    delete (L.Icon.Default.prototype as any)._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl:
        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png",
      iconUrl:
        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png",
      shadowUrl:
        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png",
    });
  }, []);

  return (
    <>
      <TileLayer
        attribution="© OpenStreetMap contributors"
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      <Marker position={position}>
        <Popup>Chiniot, Pakistan</Popup>
      </Marker>
    </>
  );
}

export default function MapClientComponent() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div
        style={{ height: "400px", width: "100%" }}
        className="flex items-center justify-center bg-gray-100 rounded-lg"
      >
        Loading Map...
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <a
        href="https://maps.app.goo.gl/MFduZGfdafGyJUAB7?g_st=aw"
        target="_blank"
        rel="noopener noreferrer"
      >
        <MapContainer
          center={position}
          zoom={13}
          className="rounded-lg"
          scrollWheelZoom={false}
          style={{ height: "100%", width: "100%" }}
        >
          <MapContent />
        </MapContainer>
      </a>
    </div>
  );
}
