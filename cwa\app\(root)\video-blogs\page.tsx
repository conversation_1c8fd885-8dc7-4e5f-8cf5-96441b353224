"use client";

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import VideoBlogGrid from '@/components/VideoBlogGrid';
import Head from 'next/head';

const VideoBlogsPage = () => {
  // Set document title and meta tags
  useEffect(() => {
    document.title = 'Video Blogs | Chinioti Wooden Art';

    // Set meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Watch our collection of video blogs showcasing the finest Chinioti wooden art and craftsmanship. Discover the artistry behind our handcrafted furniture pieces.');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'description';
      meta.content = 'Watch our collection of video blogs showcasing the finest Chinioti wooden art and craftsmanship. Discover the artistry behind our handcrafted furniture pieces.';
      document.head.appendChild(meta);
    }

    // Set meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute('content', 'Chinioti wooden art videos, furniture craftsmanship, woodworking videos, handcrafted furniture, traditional woodwork');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'keywords';
      meta.content = 'Chinioti wooden art videos, furniture craftsmanship, woodworking videos, handcrafted furniture, traditional woodwork';
      document.head.appendChild(meta);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/10 via-primary/5 to-transparent py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Video Blogs
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
              Immerse yourself in the world of Chinioti wooden art through our curated collection 
              of video content. Watch master craftsmen at work, learn about traditional techniques, 
              and discover the stories behind our exquisite furniture pieces.
            </p>
            
            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <div className="text-gray-600">Video Blogs</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">100+</div>
                <div className="text-gray-600">Hours of Content</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">10K+</div>
                <div className="text-gray-600">Total Views</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Video Grid Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <VideoBlogGrid 
              itemsPerPage={12}
              showSearch={true}
              showPagination={true}
              className="max-w-7xl mx-auto"
            />
          </motion.div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="bg-primary/5 py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Discover the Art of Chinioti Craftsmanship
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Each video tells a story of tradition, skill, and passion. From the selection 
              of premium wood to the final finishing touches, witness the journey of creating 
              timeless furniture pieces that blend heritage with modern elegance.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  Behind the Scenes
                </h3>
                <p className="text-gray-600">
                  Get an exclusive look at our workshop and meet the master craftsmen 
                  who bring each piece to life with their skilled hands and years of experience.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  Traditional Techniques
                </h3>
                <p className="text-gray-600">
                  Learn about the time-honored woodworking techniques passed down through 
                  generations, preserving the authentic Chinioti style and quality.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "Video Blogs - Chinioti Wooden Art",
            "description": "Watch our collection of video blogs showcasing the finest Chinioti wooden art and craftsmanship.",
            "url": `${process.env.NEXT_PUBLIC_SITE_URL}/video-blogs`,
            "mainEntity": {
              "@type": "VideoGallery",
              "name": "Chinioti Wooden Art Video Collection",
              "description": "A curated collection of videos showcasing traditional Chinioti woodworking techniques and craftsmanship."
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": process.env.NEXT_PUBLIC_SITE_URL
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Video Blogs",
                  "item": `${process.env.NEXT_PUBLIC_SITE_URL}/video-blogs`
                }
              ]
            }
          })
        }}
      />
    </div>
  );
};

export default VideoBlogsPage;
