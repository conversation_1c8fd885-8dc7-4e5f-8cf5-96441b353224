"use client";

import Image from "next/image";
import { useState } from "react";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  loading?: "lazy" | "eager";
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
  objectPosition?: string;
  onLoad?: () => void;
  onError?: () => void;
  // SEO-specific props
  title?: string;
  caption?: string;
  category?: string;
  location?: string;
  craftsman?: string;
  material?: string;
  style?: string;
}

/**
 * Optimized Image component with enhanced SEO alt text generation
 * Specifically designed for Chiniot furniture images
 */
export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  className = "",
  priority = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  quality = 85,
  placeholder = "empty",
  blurDataURL,
  loading = "lazy",
  objectFit = "cover",
  objectPosition = "center",
  onLoad,
  onError,
  // SEO props
  title,
  caption,
  category,
  location = "Chiniot, Punjab, Pakistan",
  craftsman,
  material,
  style,
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Generate enhanced alt text for SEO
  const generateEnhancedAlt = () => {
    let enhancedAlt = alt;
    
    // Add category if provided
    if (category) {
      enhancedAlt += ` - ${category}`;
    }
    
    // Add material information
    if (material) {
      enhancedAlt += ` made from ${material}`;
    }
    
    // Add craftsmanship context
    enhancedAlt += " handcrafted";
    
    // Add location context
    if (location) {
      enhancedAlt += ` in ${location}`;
    }
    
    // Add style information
    if (style) {
      enhancedAlt += ` featuring ${style} design`;
    }
    
    // Add craftsman information
    if (craftsman) {
      enhancedAlt += ` by ${craftsman}`;
    }
    
    // Add traditional context for Chiniot furniture
    enhancedAlt += ". Traditional Pakistani furniture craftsmanship with authentic woodworking techniques.";
    
    return enhancedAlt;
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    onLoad?.();
  };

  const handleImageError = () => {
    setImageError(true);
    onError?.();
  };

  // Fallback image for errors
  const fallbackSrc = "/assets/placeholder-furniture.jpg";

  return (
    <div className={`relative ${className}`}>
      <Image
        src={imageError ? fallbackSrc : src}
        alt={generateEnhancedAlt()}
        width={width}
        height={height}
        fill={fill}
        className={`transition-opacity duration-300 ${
          imageLoaded ? "opacity-100" : "opacity-0"
        } object-${objectFit}`}
        style={{ objectPosition }}
        priority={priority}
        sizes={sizes}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        loading={loading}
        onLoad={handleImageLoad}
        onError={handleImageError}
        title={title || alt}
      />
      
      {/* Loading placeholder */}
      {!imageLoaded && !imageError && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading...</div>
        </div>
      )}
      
      {/* Error placeholder */}
      {imageError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-gray-400 text-sm text-center p-4">
            <div className="mb-2">🖼️</div>
            <div>Image not available</div>
          </div>
        </div>
      )}
      
      {/* Caption for accessibility and SEO */}
      {caption && (
        <figcaption className="text-sm text-gray-600 mt-2 text-center">
          {caption}
        </figcaption>
      )}
    </div>
  );
}

// Helper function to generate structured data for images
export function generateImageStructuredData({
  src,
  alt,
  title,
  caption,
  category,
  location = "Chiniot, Punjab, Pakistan",
  craftsman,
  material,
  width,
  height,
}: {
  src: string;
  alt: string;
  title?: string;
  caption?: string;
  category?: string;
  location?: string;
  craftsman?: string;
  material?: string;
  width?: number;
  height?: number;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "ImageObject",
    contentUrl: src,
    name: title || alt,
    description: caption || alt,
    ...(width && { width }),
    ...(height && { height }),
    creator: {
      "@type": "Person",
      name: craftsman || "Chiniot Artisan",
      address: {
        "@type": "PostalAddress",
        addressLocality: "Chiniot",
        addressRegion: "Punjab",
        addressCountry: "Pakistan",
      },
    },
    about: {
      "@type": "Product",
      name: title || alt,
      category: category || "Furniture",
      material: material || "Wood",
      manufacturer: {
        "@type": "Organization",
        name: "Chinioti Wooden Art",
        address: {
          "@type": "PostalAddress",
          addressLocality: location,
        },
      },
    },
    keywords: [
      "Chiniot furniture",
      "handcrafted wooden furniture",
      "Pakistani furniture",
      "traditional craftsmanship",
      category,
      material,
    ].filter(Boolean).join(", "),
  };
}

// Common image configurations for different use cases
export const imageConfigs = {
  productCard: {
    sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
    quality: 85,
    loading: "lazy" as const,
  },
  productDetail: {
    sizes: "(max-width: 768px) 100vw, 50vw",
    quality: 90,
    priority: true,
  },
  hero: {
    sizes: "100vw",
    quality: 90,
    priority: true,
  },
  gallery: {
    sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw",
    quality: 80,
    loading: "lazy" as const,
  },
};
