"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { getCurrentUser, updateUserProfile } from "@/apis/user";
import { User } from "@/types/user";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { UserCircle, Mail, Phone, Save, Edit, X, Lock } from "lucide-react";
import PasswordChangeForm from "@/components/PasswordChangeForm";

const ProfilePage = () => {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
  });

  useEffect(() => {
    // Redirect if not authenticated
    if (!isAuthenticated && !isLoading) {
      router.push("/sign-in");
      return;
    }

    const fetchUserProfile = async () => {
      setIsLoading(true);
      try {
        const userData = await getCurrentUser();
        if (userData) {
          setUser(userData);
          setFormData({
            name: userData.name,
            email: userData.email,
            phone: userData.phone || "",
          });
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
        toast.error("Failed to load profile. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchUserProfile();
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      toast.error("User ID not found");
      return;
    }

    setIsLoading(true);
    try {
      const updatedUser = await updateUserProfile(user.id, formData);
      if (updatedUser) {
        setUser(updatedUser);
        toast.success("Profile updated successfully");
        setIsEditing(false);
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast.error(error.response?.data?.message || "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const cancelEdit = () => {
    // Reset form data to current user data
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone || "",
      });
    }
    setIsEditing(false);
  };

  const togglePasswordForm = () => {
    setShowPasswordForm(!showPasswordForm);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center">
        <div className="animate-pulse">Loading profile...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Profile Not Found</h1>
        <p className="mb-8">
          Unable to load your profile. Please try again later.
        </p>
        <Button onClick={() => router.push("/")}>Go Home</Button>
      </div>
    );
  }

  return (
    <div className="profile-container container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="profile-card max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* Profile Header */}
        <div className="profile-header bg-accent p-4 sm:p-6 text-white">
          <h1 className="text-xl sm:text-2xl font-bold">My Profile</h1>
          <p className=" text-sm sm:text-base text-white">
            Manage your account information
          </p>
        </div>

        {/* Profile Content */}
        <div className="profile-content p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <div className="profile-avatar w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                {user.avatar ? (
                  <Image
                    src={user.avatar}
                    alt={user.name}
                    width={64}
                    height={64}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <UserCircle size={32} className="text-gray-400 sm:w-10 sm:h-10" />
                )}
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="profile-name text-lg sm:text-xl font-semibold truncate">{user.name}</h2>
                <p className="profile-email text-gray-500 text-sm sm:text-base truncate">{user.email}</p>
              </div>
            </div>
            {!isEditing ? (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
                className="flex items-center gap-2 w-full sm:w-auto justify-center"
                size="sm"
              >
                <Edit size={16} />
                <span className="hidden xs:inline">Edit Profile</span>
                <span className="xs:hidden">Edit</span>
              </Button>
            ) : null}
          </div>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4 sm:space-y-6">
              {/* Name Field */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2 text-sm sm:text-base">
                  <UserCircle size={16} className="flex-shrink-0" />
                  <span>Full Name</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                    pattern="^[A-Za-z\s]+$"
                    title="Name can only contain alphabets and spaces"
                    maxLength={25}
                    className="text-sm sm:text-base"
                  />
                ) : (
                  <div className="p-2 sm:p-3 border rounded-md bg-gray-50 text-sm sm:text-base break-words">
                    {user.name}
                  </div>
                )}
              </div>

              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2 text-sm sm:text-base">
                  <Mail size={16} className="flex-shrink-0" />
                  <span>Email Address</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                    className="text-sm sm:text-base"
                  />
                ) : (
                  <div className="p-2 sm:p-3 border rounded-md bg-gray-50 text-sm sm:text-base break-all">
                    {user.email}
                  </div>
                )}
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2 text-sm sm:text-base">
                  <Phone size={16} className="flex-shrink-0" />
                  <span>Phone Number</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                    pattern="^\d{11}$"
                    title="Phone number must be exactly 11 digits"
                    className="text-sm sm:text-base"
                  />
                ) : (
                  <div className="p-2 sm:p-3 border rounded-md bg-gray-50 text-sm sm:text-base">
                    {user.phone || "Not provided"}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              {isEditing && (
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mt-6">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="flex items-center justify-center gap-2 w-full sm:w-auto"
                    size="sm"
                  >
                    <Save size={16} />
                    <span>Save Changes</span>
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={cancelEdit}
                    disabled={isLoading}
                    className="flex items-center justify-center gap-2 w-full sm:w-auto"
                    size="sm"
                  >
                    <X size={16} />
                    <span>Cancel</span>
                  </Button>
                </div>
              )}
            </div>
          </form>

          {/* Password Change Section */}
          {!isEditing && (
            <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t">
              {!showPasswordForm ? (
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
                  <div className="flex-1">
                    <h3 className="text-base sm:text-lg font-semibold">Password</h3>
                    <p className="text-xs sm:text-sm text-gray-500">
                      Change your account password
                    </p>
                  </div>
                  <Button
                    onClick={togglePasswordForm}
                    variant="outline"
                    className="flex items-center justify-center gap-2 w-full sm:w-auto"
                    size="sm"
                  >
                    <Lock size={16} />
                    <span className="hidden xs:inline">Change Password</span>
                    <span className="xs:hidden">Change</span>
                  </Button>
                </div>
              ) : (
                <PasswordChangeForm
                  userId={user.id}
                  onCancel={togglePasswordForm}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
