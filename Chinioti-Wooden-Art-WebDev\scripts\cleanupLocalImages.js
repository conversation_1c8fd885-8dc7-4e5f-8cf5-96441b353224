/**
 * <PERSON><PERSON>t to clean up local image files after migration to GridFS
 * 
 * This script:
 * 1. Connects to MongoDB and initializes GridFS
 * 2. Gets all products from the database
 * 3. For each product, checks if its images exist in GridFS
 * 4. If all images exist in GridFS, deletes the local files
 * 5. Keeps a .gitkeep file in the uploads directory to maintain the directory structure
 */
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');
const Product = require('../models/Product');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Initialize GridFS bucket
let gridFSBucket;

// Connect to GridFS
const connectGridFS = async () => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      throw new Error('MongoDB not connected');
    }

    // Initialize GridFS bucket
    gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS connected successfully');
    return true;
  } catch (error) {
    console.error('GridFS connection error:', error);
    return false;
  }
};

// Check if a file exists in GridFS
const fileExistsInGridFS = async (filename) => {
  try {
    const files = await gridFSBucket.find({ filename }).toArray();
    return files.length > 0;
  } catch (error) {
    console.error(`Error checking if file ${filename} exists in GridFS:`, error);
    return false;
  }
};

// Delete a local file
const deleteLocalFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Deleted local file: ${filePath}`);
      return true;
    } else {
      console.log(`File not found: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`Error deleting local file ${filePath}:`, error);
    return false;
  }
};

// Main cleanup function
const cleanupLocalImages = async () => {
  try {
    // Connect to MongoDB and GridFS
    await connectDB();
    await connectGridFS();
    
    // Get all products
    const products = await Product.find();
    console.log(`Found ${products.length} products to process`);
    
    // Keep track of processed files
    const processedFiles = new Set();
    let deletedCount = 0;
    let errorCount = 0;
    
    // Process each product
    for (const product of products) {
      console.log(`Processing product: ${product.name} (${product._id})`);
      
      // Process main image
      if (product.image && !processedFiles.has(product.image)) {
        processedFiles.add(product.image);
        
        // Check if image exists in GridFS
        const existsInGridFS = await fileExistsInGridFS(product.image);
        
        if (existsInGridFS) {
          // Delete local file
          const imagePath = path.join(__dirname, '../uploads', product.image);
          const deleted = deleteLocalFile(imagePath);
          if (deleted) deletedCount++;
          else errorCount++;
        } else {
          console.log(`Main image ${product.image} not found in GridFS, keeping local file`);
        }
      }
      
      // Process additional images
      if (product.images && product.images.length > 0) {
        for (const imageName of product.images) {
          if (!processedFiles.has(imageName)) {
            processedFiles.add(imageName);
            
            // Check if image exists in GridFS
            const existsInGridFS = await fileExistsInGridFS(imageName);
            
            if (existsInGridFS) {
              // Delete local file
              const imagePath = path.join(__dirname, '../uploads', imageName);
              const deleted = deleteLocalFile(imagePath);
              if (deleted) deletedCount++;
              else errorCount++;
            } else {
              console.log(`Additional image ${imageName} not found in GridFS, keeping local file`);
            }
          }
        }
      }
    }
    
    // Create .gitkeep file to maintain directory structure
    const gitkeepPath = path.join(__dirname, '../uploads', '.gitkeep');
    if (!fs.existsSync(gitkeepPath)) {
      fs.writeFileSync(gitkeepPath, '');
      console.log('Created .gitkeep file in uploads directory');
    }
    
    console.log(`Cleanup completed: ${deletedCount} files deleted, ${errorCount} errors`);
    process.exit(0);
  } catch (error) {
    console.error('Cleanup failed:', error);
    process.exit(1);
  }
};

// Run the cleanup
cleanupLocalImages();
