"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { useTermsAcceptance } from "@/contexts/TermsAcceptanceContext";
import { useModal } from "@/contexts/ModalContext";
import { ExternalLink, Shield, FileText, Eye } from "lucide-react";
import Link from "next/link";

const TermsAcceptanceModal = () => {
  const { showModal, handleAcceptTerms, handleDeclineTerms } =
    useTermsAcceptance();
  const { openModal, closeModal } = useModal();
  const [showFullTerms, setShowFullTerms] = useState(false);

  // Register this modal with the modal context
  React.useEffect(() => {
    if (showModal) {
      openModal("terms-acceptance");
    } else if (!showModal) {
      closeModal("terms-acceptance");
    }
  }, [showModal]); // Only include showModal in the dependency array

  const handleAccept = () => {
    handleAcceptTerms();
    closeModal("terms-acceptance");
  };

  const handleDecline = () => {
    handleDeclineTerms();
    closeModal("terms-acceptance");
  };

  const toggleFullTerms = () => {
    setShowFullTerms(!showFullTerms);
  };

  return (
    <AnimatePresence>
      {showModal && (
        <Dialog open={showModal} onOpenChange={() => {}}>
          <DialogContent className="max-w-2xl">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <DialogHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-accent/10 rounded-full">
                    <Shield className="w-6 h-6 text-accent" />
                  </div>
                  <DialogTitle className="text-xl">
                    Welcome to Chinioti Wooden Art
                  </DialogTitle>
                </div>
                <DialogDescription className="text-left">
                  Before you continue, please review and accept our Terms of
                  Service and Privacy Policy to ensure the best experience on
                  our platform.
                </DialogDescription>
              </DialogHeader>

              <div className="my-6">
                {!showFullTerms ? (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        Quick Summary:
                      </h3>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>
                          • We respect your privacy and protect your personal
                          information
                        </li>
                        <li>
                          • Our products are handcrafted with traditional
                          Chinioti craftsmanship
                        </li>
                        <li>
                          • We provide secure payment processing and reliable
                          delivery
                        </li>
                        <li>• Customer satisfaction is our top priority</li>
                      </ul>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFullTerms}
                        className="flex items-center gap-2"
                      >
                        <Eye className="w-4 h-4" />
                        Read Full Terms
                      </Button>
                      <Link href="/terms-of-service" target="_blank">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <FileText className="w-4 h-4" />
                          Terms of Service
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </Link>
                      <Link href="/privacy-policy" target="_blank">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <Shield className="w-4 h-4" />
                          Privacy Policy
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="max-h-60 overflow-y-auto bg-gray-50 p-4 rounded-lg text-sm">
                      <h3 className="font-semibold mb-2">
                        Terms of Service - Key Points:
                      </h3>
                      <div className="space-y-2 text-gray-700">
                        <p>
                          <strong>1. Product Quality:</strong> All our furniture
                          is handcrafted using traditional Chinioti techniques
                          with premium wood materials.
                        </p>
                        <p>
                          <strong>2. Orders & Payment:</strong> We accept secure
                          online payments and process orders within 1-2 business
                          days.
                        </p>
                        <p>
                          <strong>3. Delivery:</strong> We provide reliable
                          delivery across Pakistan with tracking information.
                        </p>
                        <p>
                          <strong>4. Returns:</strong> 7-day return policy for
                          damaged or defective items.
                        </p>
                        <p>
                          <strong>5. Privacy:</strong> We protect your personal
                          information and never share it with third parties
                          without consent.
                        </p>
                        <p>
                          <strong>6. Custom Orders:</strong> We accept custom
                          furniture requests with detailed specifications.
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleFullTerms}
                      className="flex items-center gap-2"
                    >
                      Show Summary
                    </Button>
                  </div>
                )}
              </div>

              <DialogFooter>
                <div className="flex flex-col sm:flex-row gap-3 w-full">
                  <Button
                    variant="outline"
                    onClick={handleDecline}
                    className="flex-1 sm:flex-none"
                  >
                    I'll Review Later
                  </Button>
                  <Button
                    onClick={handleAccept}
                    className="flex-1 sm:flex-none bg-[#000000] text-white"
                  >
                    I Accept Terms & Privacy Policy
                  </Button>
                </div>
              </DialogFooter>

              <div className="mt-4 text-xs text-gray-500 text-center">
                By accepting, you agree to our Terms of Service and Privacy
                Policy. You can review these documents anytime in our footer.
              </div>
            </motion.div>
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  );
};

export default TermsAcceptanceModal;
