import apiClient from "@/lib/api/apiClient";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";

// Cache for products to avoid unnecessary API calls
let productsCache: ProductData[] | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Map backend product model to frontend ProductData type
 */
const mapBackendProductToFrontend = (backendProduct: any): ProductData => {
  return {
    id: backendProduct._id || backendProduct.id || "",
    type: backendProduct.type || "buy",
    video: backendProduct.video || undefined,
    images:
      backendProduct.images && backendProduct.images.length
        ? backendProduct.images
        : backendProduct.image
        ? [backendProduct.image]
        : [IMAGE_PLACEHOLDER_URL],
    image:
      backendProduct.image || backendProduct.images[0] || IMAGE_PLACEHOLDER_URL,
    title: backendProduct.name || "A Nice Product",
    name: backendProduct.name || "A Product",
    category: backendProduct.category || "Uncategorized",
    available: backendProduct.stock !== undefined ? backendProduct.stock : true,
    discount: backendProduct.discount || "0",
    price: backendProduct.price || 0,
    description: backendProduct.description,
    quantity: backendProduct.quantity,
    created: backendProduct.created,
  };
};

/**
 * Fetch all products from the backend API
 */
export const allProducts = async (): Promise<ProductData[]> => {
  const currentTime = Date.now();

  // Return cached products if they exist and are not expired
  if (productsCache && currentTime - lastFetchTime < CACHE_DURATION) {
    console.log("Using cached products data");
    return productsCache;
  }

  try {
    console.log("Fetching products from API");
    const response = await apiClient.get("/products");

    if (response.data && response.data.status === "success") {
      const backendProducts = response.data.data.products;
      const mappedProducts = backendProducts.map(mapBackendProductToFrontend);

      // Update cache
      productsCache = mappedProducts;
      lastFetchTime = currentTime;

      return mappedProducts;
    } else {
      console.error("API returned unexpected format:", response.data);
      return [];
    }
  } catch (error) {
    console.error("Error fetching products:", error);
    // Return empty array or cached data if available
    return productsCache || [];
  }
};

/**
 * Get featured products (top 6 products)
 */
export const featuredProducts = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // Return first 6 products or fewer if not enough products
  return allProductsData.slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get new arrivals (most recent products based on created date)
 */
export const newArrivals = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // Sort by created date (newest first) and take first 6
  return [...allProductsData]
    .sort((a, b) => {
      const dateA = a.created ? new Date(a.created).getTime() : 0;
      const dateB = b.created ? new Date(b.created).getTime() : 0;
      return dateB - dateA;
    })
    .slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get trending products (for now, just return a different set of products)
 */
export const trendingProducts = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // For now, just return a different subset of products
  // In a real app, this would be based on sales data or views
  return allProductsData
    .filter((_, index) => index % 2 === 0) // Just an example to get a different subset
    .slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get products with optional filter
 */
export const getProducts = async (filter?: string): Promise<ProductData[]> => {
  try {
    switch (filter) {
      case "featured":
        return await featuredProducts();
      case "new":
        return await newArrivals();
      case "trending":
        return await trendingProducts();
      default:
        return await allProducts();
    }
  } catch (error) {
    console.error(`Error getting products with filter ${filter}:`, error);
    return [];
  }
};

/**
 * Get a single product by ID
 */
export const getProductById = async (
  id: string
): Promise<ProductData | undefined> => {
  try {
    // Try to find in cache first
    const cachedProducts = productsCache;
    if (cachedProducts) {
      const cachedProduct = cachedProducts.find((p) => p.id === id);
      if (cachedProduct) {
        return cachedProduct;
      }
    }

    // If not in cache, fetch from API
    const response = await apiClient.get(`/products/${id}`);

    if (response.data && response.data.status === "success") {
      return mapBackendProductToFrontend(response.data.data.product);
    } else {
      console.error("API returned unexpected format:", response.data);
      return undefined;
    }
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);
    return undefined;
  }
};

/**
 * Get related products (products in the same category)
 */
export const getRelatedProducts = async (
  id: string,
  limit: number = 4
): Promise<ProductData[]> => {
  try {
    const product = await getProductById(id);
    if (!product) return [];

    const allProductsData = await allProducts();

    // Get products in the same category
    return allProductsData
      .filter((p) => p.id !== id && p.category === product.category)
      .slice(0, limit);
  } catch (error) {
    console.error(`Error fetching related products for ID ${id}:`, error);
    return [];
  }
};


