const express = require('express');
const router = express.Router();
const OrderController = require('../controllers/orderController');
const { cacheMiddleware } = require('../config/redis');

const app = require('express')();

router.route('/').post(OrderController.createOrder);
router.route('/').get(cacheMiddleware(300), OrderController.getAllOrders);
router.route('/:id').get(cacheMiddleware(300), OrderController.getOrder);
router.route('/:id').patch(OrderController.updateOrder);
router.route('/:id').delete(OrderController.deleteOrder);

module.exports = router;